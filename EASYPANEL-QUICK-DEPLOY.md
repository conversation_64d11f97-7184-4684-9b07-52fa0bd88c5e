# 🚀 EasyPanel Quick Deploy Guide - Ider Kopi

## 🎯 Metode Deployment yang Direkomendasikan

### Option 1: Static Site (PALING MUDAH) ⭐

1. **Login ke EasyPanel Dashboard**
2. **Create New App**
   - Pilih "Static Site" atau "React/Vite"
   - Connect ke Git repository Anda

3. **Build Configuration**
   ```
   Framework: React/Vite
   Build Command: npm run build
   Output Directory: dist
   Node Version: 18
   Install Command: npm ci
   ```

4. **Environment Variables**
   Set di EasyPanel Dashboard:
   ```
   VITE_SUPABASE_URL=https://hsgkjxnslkxsvefsouxi.supabase.co
   VITE_SUPABASE_ANON_KEY=your-anon-key
   NODE_ENV=production
   ```

5. **Deploy** - Klik tombol Deploy

### Option 2: Docker (ADVANCED)

Jika static site tidak berhasil, gunakan Docker:

1. **Pastikan files ada:**
   - ✅ Dockerfile (sudah dibuat)
   - ✅ nginx.conf (sudah dibuat)
   - ✅ docker-compose.yml (sudah dibuat)

2. **Test lokal dulu:**
   ```bash
   # Build dan test
   npm run build
   docker build -t ider-kopi .
   docker run -p 8080:80 ider-kopi
   
   # Test health check
   curl http://localhost:8080/health
   ```

3. **Deploy ke EasyPanel:**
   - Pilih "Docker"
   - Set Dockerfile path: `./Dockerfile`
   - Port: 80

## 🔧 Troubleshooting

### Error: Build Failed
```bash
# Cek di EasyPanel build logs
# Kemungkinan penyebab:
1. Environment variables tidak diset
2. Node version salah (harus 18)
3. npm ci gagal
```

**Solusi:**
- Pastikan VITE_SUPABASE_URL dan VITE_SUPABASE_ANON_KEY sudah diset
- Gunakan Node 18
- Coba ganti `npm ci` dengan `npm install`

### Error: App tidak load
```bash
# Cek runtime logs di EasyPanel
# Kemungkinan penyebab:
1. Health check gagal
2. Port salah
3. Static files tidak ditemukan
```

**Solusi:**
- Pastikan health check endpoint: `/health`
- Port harus 80 untuk Docker, auto untuk static site
- Pastikan output directory: `dist`

### Error: Supabase connection failed
```bash
# Cek environment variables
# Pastikan:
1. VITE_SUPABASE_URL format benar
2. VITE_SUPABASE_ANON_KEY valid
3. Supabase project aktif
```

## 📋 Checklist Sebelum Deploy

- [ ] Code sudah di-push ke Git repository
- [ ] Environment variables sudah disiapkan
- [ ] Supabase project aktif dan accessible
- [ ] Build berhasil lokal: `npm run build`
- [ ] Preview berhasil lokal: `npm run preview`

## 🎯 Quick Commands

```bash
# Test build lokal
npm run build
npm run preview

# Test Docker lokal
docker build -t ider-kopi .
docker run -p 8080:80 ider-kopi

# Health check
curl http://localhost:8080/health

# Deploy preparation
git add .
git commit -m "Ready for EasyPanel deployment"
git push origin main
```

## 📞 Jika Masih Gagal

1. **Coba Static Site dulu** - Paling mudah dan reliable
2. **Check build logs** - Lihat error message di EasyPanel
3. **Test lokal** - Pastikan `npm run build` berhasil
4. **Environment variables** - Double check semua variable sudah benar

## 🎉 Success Indicators

Setelah deploy berhasil:
- ✅ Website load di browser
- ✅ Health check: `https://your-domain.com/health` return "healthy"
- ✅ Slider berfungsi
- ✅ Navigation menu berfungsi
- ✅ Authentication bisa login/register

**Ready to deploy! 🚀**
