-- <PERSON><PERSON><PERSON> to fix contact information access
-- Run this in Supabase SQL Editor

-- Drop existing restrictive policy for site_content
DROP POLICY IF EXISTS "Ad<PERSON> can manage site content" ON public.site_content;

-- Create new policies for site_content
-- Allow public read access to public content sections
CREATE POLICY "Public can read public content" ON public.site_content
  FOR SELECT USING (section IN ('hero', 'about', 'contact'));

-- <PERSON><PERSON> can manage all site content
CREATE POLICY "<PERSON><PERSON> can manage site content" ON public.site_content
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE id = auth.uid() AND role = 'admin'
    )
  );

-- Update contact phone number to match the one used in Kontak.tsx
UPDATE public.site_content 
SET value = '************-01' 
WHERE section = 'contact' AND key = 'phone';

-- Verify the changes
SELECT section, key, value FROM public.site_content WHERE section = 'contact';
