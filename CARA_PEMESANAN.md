# Cara Pemesanan I<PERSON> via WhatsApp

## Untuk Customer (Pelanggan)

### 1. Buka Website Ider Kopi
- Kunjungi website Ider Kopi
- Pilih menu "Menu" di navigasi

### 2. <PERSON><PERSON><PERSON>
- Browse berbagai pilihan kopi yang tersedia
- Lihat detail harga dan deskripsi setiap menu
- Klik tombol "Tambah ke Keranjang" untuk menu yang diinginkan

### 3. <PERSON><PERSON><PERSON> Keran<PERSON>g Belanja
- Klik ikon keranjang di pojok kanan atas untuk melihat pesanan
- Atur jumlah item dengan tombol + dan -
- Hapus item yang tidak diinginkan dengan tombol sampah
- Lihat total harga pesanan

### 4. Checkout dan <PERSON>
- <PERSON><PERSON> tombol "Pesan via WhatsApp" di keranjang
- Isi form dengan data lengkap:
  - **<PERSON>a <PERSON>** (wajib)
  - **Nomor WhatsApp** (wajib, format: 08xxxxxxxxxx)
  - **<PERSON><PERSON><PERSON>** (wajib, untuk pengiriman)
  - **<PERSON><PERSON><PERSON>** (opsional, misal: gula sedikit, es banyak)

### 5. <PERSON><PERSON>
- <PERSON><PERSON> tombol "Pesan via WhatsApp"
- Sistem akan otomatis membuka WhatsApp dengan pesan yang sudah diformat
- Pesan berisi detail lengkap pesanan Anda
- Klik "Send" di WhatsApp untuk mengirim pesanan

### 6. Tunggu Konfirmasi
- Tim Ider Kopi akan membalas pesan WhatsApp Anda
- Konfirmasi ketersediaan menu dan estimasi waktu
- Proses pembayaran dan pengiriman akan dikoordinasikan via WhatsApp

## Untuk Admin/Pemilik Bisnis

### 1. Setup Awal
- Pastikan sudah login sebagai admin
- Akses halaman `/setup-admin` jika belum menjadi admin
- Promosikan akun email Anda menjadi admin

### 2. Kelola Menu
- Masuk ke halaman Admin
- Tambah, edit, atau hapus menu kopi
- Upload gambar menu yang menarik
- Set harga dan deskripsi yang jelas

### 3. Setup WhatsApp Business
- Siapkan nomor WhatsApp bisnis
- Install WAHA API (lihat WHATSAPP_SETUP.md)
- Konfigurasi environment variables
- Test sistem pemesanan

### 4. Terima dan Proses Pesanan
- Monitor WhatsApp bisnis untuk pesanan masuk
- Balas dengan konfirmasi ketersediaan
- Koordinasikan pembayaran (transfer/COD)
- Atur jadwal pengiriman/pickup

## Format Pesan Pesanan Otomatis

Sistem akan generate pesan dengan format berikut:

```
🛒 *PESANAN BARU - IDER KOPI*

📅 *Tanggal:* [Tanggal dan waktu otomatis]

👤 *Data Pelanggan:*
• Nama: [Nama customer]
• No. HP: [Nomor WhatsApp customer]
• Alamat: [Alamat lengkap customer]

☕ *Detail Pesanan:*
1. [Nama Menu]
   [Jumlah]x [Harga] = [Subtotal]
2. [Menu lainnya...]

💰 *Total: [Total Harga]*

📝 *Catatan:* [Catatan customer jika ada]

🚚 *Status:* Menunggu Konfirmasi

Terima kasih telah memesan di Ider Kopi! 🙏
```

## Tips untuk Customer

### 1. Informasi Lengkap
- Pastikan nomor WhatsApp aktif dan bisa dihubungi
- Berikan alamat yang detail dan mudah ditemukan
- Cantumkan patokan/landmark jika perlu

### 2. Waktu Pemesanan
- Pesan di jam operasional untuk respon cepat
- Berikan waktu yang cukup untuk persiapan
- Konfirmasi ketersediaan untuk pesanan besar

### 3. Pembayaran
- Siapkan uang pas untuk COD
- Untuk transfer, tunggu konfirmasi rekening
- Simpan bukti pembayaran

## Tips untuk Admin

### 1. Respon Cepat
- Monitor WhatsApp bisnis secara rutin
- Balas pesanan dalam 15-30 menit
- Gunakan template balasan untuk efisiensi

### 2. Manajemen Stok
- Update ketersediaan menu secara real-time
- Nonaktifkan menu yang habis stok
- Informasikan estimasi restock

### 3. Customer Service
- Berikan informasi yang jelas dan lengkap
- Konfirmasi detail pesanan sebelum proses
- Follow up untuk kepuasan customer

## Template Balasan WhatsApp

### Konfirmasi Pesanan
```
Halo [Nama Customer]! 👋

Terima kasih atas pesanannya:
[Copy detail pesanan]

✅ Pesanan dikonfirmasi
⏰ Estimasi siap: [Waktu]
💳 Total pembayaran: [Jumlah]

Pilihan pembayaran:
1. Transfer: BCA 1234567890 a.n. Ider Kopi
2. COD saat pengiriman

Mohon konfirmasi metode pembayaran yang dipilih. Terima kasih! 🙏
```

### Pesanan Siap
```
Halo [Nama Customer]! ☕

Pesanan Anda sudah siap:
[Detail pesanan]

📍 Alamat pengiriman: [Alamat]
🚚 Driver sedang dalam perjalanan
⏰ Estimasi tiba: [Waktu]

Terima kasih sudah mempercayai Ider Kopi! 🙏
```

## Troubleshooting

### Customer
- **WhatsApp tidak terbuka**: Pastikan WhatsApp terinstall di HP
- **Pesan tidak terformat**: Coba refresh halaman dan ulangi
- **Keranjang kosong**: Pastikan sudah menambah item ke keranjang

### Admin
- **Tidak bisa akses admin**: Gunakan halaman `/setup-admin`
- **WAHA tidak jalan**: Cek dokumentasi WHATSAPP_SETUP.md
- **Pesanan tidak masuk**: Verifikasi nomor WhatsApp bisnis

## Support

Jika ada kendala teknis, hubungi developer atau cek dokumentasi lengkap di repository GitHub.
