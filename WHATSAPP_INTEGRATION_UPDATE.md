# Update WhatsApp Integration - Environment Variables & Database Phone

## Perubahan yang <PERSON>

### 1. Environment Variables Fix
**Masalah**: Environment variables menggunakan prefix `REACT_APP_` yang tidak bekerja di Vite
**Solusi**: Men<PERSON>bah ke prefix `VITE_` yang benar untuk Vite

#### File yang Diubah:
- `.env.example` - Updated prefix dari `REACT_APP_` ke `VITE_`
- `.env` - Updated konfigurasi WAHA API
- `src/components/Checkout.tsx` - Menggunakan `import.meta.env.VITE_*`
- `WHATSAPP_SETUP.md` - Updated dokumentasi

#### Environment Variables Baru:
```env
# WhatsApp API WAHA Configuration
VITE_WAHA_BASE_URL=https://explore-waplustsa.poe7ey.easypanel.host
VITE_WAHA_SESSION=hammam_bot

# Business Information
VITE_BUSINESS_NAME=Ider Kopi Ungaran
VITE_BUSINESS_ADDRESS=Jl. <PERSON>a <PERSON>gara<PERSON>, Semarang
```

### 2. Database Phone Integration
**Masalah**: Nomor WhatsApp hardcoded di komponen
**Solusi**: Mengambil nomor dari database `site_content` table

#### Implementasi:
- Fetch nomor dari `site_content` dengan `section='contact'` dan `key='phone'`
- Konversi format otomatis dari `************-01` ke `6281533100001`
- Fallback ke nomor dari `Kontak.tsx` jika database gagal
- Logging untuk debugging

#### Code Changes di `Checkout.tsx`:
```typescript
const [businessPhone, setBusinessPhone] = useState<string>('6281234567890');

useEffect(() => {
  const fetchBusinessPhone = async () => {
    try {
      const { data, error } = await supabase
        .from('site_content')
        .select('value')
        .eq('section', 'contact')
        .eq('key', 'phone')
        .single();

      if (data?.value) {
        // Convert phone format
        const cleanPhone = data.value.replace(/[^\d]/g, '');
        let formattedPhone: string;
        if (cleanPhone.startsWith('0')) {
          formattedPhone = '62' + cleanPhone.substring(1);
        } else if (cleanPhone.startsWith('62')) {
          formattedPhone = cleanPhone;
        } else {
          formattedPhone = '62' + cleanPhone;
        }
        setBusinessPhone(formattedPhone);
      }
    } catch (error) {
      // Fallback to Kontak.tsx number
      setBusinessPhone('6281533100001');
    }
  };
  fetchBusinessPhone();
}, []);
```

### 3. Database Policy Fix
**Masalah**: RLS policy tidak mengizinkan pembacaan data kontak tanpa autentikasi
**Solusi**: Membuat policy baru untuk akses publik ke data kontak

#### SQL Script (`fix_contact_policy.sql`):
```sql
-- Allow public read access to public content sections
CREATE POLICY "Public can read public content" ON public.site_content
  FOR SELECT USING (section IN ('hero', 'about', 'contact'));

-- Update contact phone number
UPDATE public.site_content 
SET value = '************-01' 
WHERE section = 'contact' AND key = 'phone';
```

## Cara Testing

### 1. Test Environment Variables
1. Buka Developer Tools → Console
2. Navigasi ke halaman Menu
3. Tambah item ke keranjang
4. Klik "Pesan via WhatsApp"
5. Lihat console log untuk konfigurasi WAHA:
```
WAHA Config: {
  baseUrl: "https://explore-waplustsa.poe7ey.easypanel.host",
  session: "hammam_bot",
  businessPhone: "6281533100001"
}
```

### 2. Test Database Phone Fetch
1. Buka Developer Tools → Console
2. Lihat log saat komponen Checkout dimuat:
```
Fetching business phone from database...
Database response: { data: { value: "************-01" }, error: null }
Raw phone from database: ************-01
Cleaned phone: 081533100001
Formatted phone for WhatsApp: 6281533100001
```

### 3. Test WhatsApp Integration
1. Tambah item ke keranjang
2. Isi form checkout
3. Klik "Kirim Pesanan"
4. Pesan akan dikirim ke nomor dari database (bukan hardcoded)

## Troubleshooting

### Environment Variables Tidak Terbaca
- Pastikan menggunakan prefix `VITE_` bukan `REACT_APP_`
- Restart development server setelah mengubah `.env`
- Cek console log untuk nilai environment variables

### Database Phone Tidak Terbaca
- Jalankan SQL script `fix_contact_policy.sql` di Supabase SQL Editor
- Pastikan RLS policy mengizinkan pembacaan data kontak
- Cek console log untuk error database

### WAHA API Tidak Bekerja
- Pastikan WAHA server berjalan di URL yang benar
- Cek session name sesuai dengan konfigurasi WAHA
- Sistem akan fallback ke WhatsApp Web jika WAHA gagal

## Next Steps

1. **Jalankan SQL Script**: Execute `fix_contact_policy.sql` di Supabase SQL Editor
2. **Update Production Environment**: Set environment variables di EasyPanel
3. **Test End-to-End**: Test complete ordering flow dengan nomor WhatsApp yang benar
4. **Monitor Logs**: Pantau console logs untuk memastikan semua bekerja dengan baik
