# 🚀 Deployment Guide - EasyPanel

Panduan lengkap untuk deploy website Ider Kopi ke EasyPanel.

## 📋 Prerequisites

- [x] Akun EasyPanel aktif
- [x] Repository Git (GitHub/GitLab/Bitbucket)
- [x] Supabase project dengan database setup
- [x] Domain name (opsional)

## 🔧 Persiapan Deployment

### 1. Environment Variables

Siapkan environment variables berikut di EasyPanel:

```bash
# Supabase Configuration (WAJIB)
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# Application Configuration
NODE_ENV=production
VITE_APP_TITLE=Ider Kopi Ungaran
VITE_APP_DESCRIPTION=Kopi Keliling Halal #1 di Ungaran
```

### 2. Database Setup

Pastikan database Supabase sudah setup dengan:
- [x] Tabel `profiles` untuk user management
- [x] Tabel `hero_slides` untuk slider
- [x] Tabel `menu_items` untuk menu
- [x] Tabel `site_content` untuk konten dinamis
- [x] RLS policies sudah dikonfigurasi

## 🚀 Deployment Steps

### Step 1: Push ke Repository

```bash
# Add all files
git add .

# Commit changes
git commit -m "Ready for EasyPanel deployment"

# Push to repository
git push origin main
```

### Step 2: Connect Repository di EasyPanel

1. Login ke EasyPanel dashboard
2. Klik "Create New App"
3. Pilih "From Git Repository"
4. Connect repository Anda
5. Pilih branch `main`

### Step 3: Configure Build Settings

```yaml
# Build Configuration
Build Command: npm run build
Output Directory: dist
Node Version: 18
Install Command: npm ci
```

### Step 4: Set Environment Variables

Di EasyPanel dashboard:
1. Go to App Settings → Environment
2. Add environment variables:
   - `VITE_SUPABASE_URL`
   - `VITE_SUPABASE_ANON_KEY`
   - `NODE_ENV=production`

### Step 5: Configure Domain (Optional)

1. Go to App Settings → Domains
2. Add your custom domain
3. Configure DNS records:
   ```
   Type: A
   Name: @
   Value: [EasyPanel IP]
   
   Type: CNAME
   Name: www
   Value: your-domain.com
   ```

### Step 6: Deploy

1. Klik "Deploy" button
2. Monitor build logs
3. Wait for deployment completion

## 🔍 Verification

### Health Check

Setelah deployment, verify:

```bash
# Check health endpoint
curl https://your-domain.com/health

# Expected response: "healthy"
```

### Functionality Test

1. ✅ Homepage loads dengan slider
2. ✅ Navigation menu berfungsi
3. ✅ Authentication system bekerja
4. ✅ Admin panel accessible
5. ✅ Database connection aktif

## 🛠 Troubleshooting

### Common Issues

#### 1. Build Failed
```bash
# Check build logs in EasyPanel
# Common causes:
- Missing environment variables
- Node version mismatch
- Dependency conflicts
```

#### 2. App Not Loading
```bash
# Check runtime logs
# Common causes:
- Nginx configuration issues
- Port binding problems
- Health check failures
```

#### 3. Database Connection Failed
```bash
# Verify environment variables:
- VITE_SUPABASE_URL is correct
- VITE_SUPABASE_ANON_KEY is valid
- Supabase project is active
```

#### 4. Authentication Issues
```bash
# Check Supabase settings:
- Site URL configured correctly
- Redirect URLs include your domain
- RLS policies are active
```

### Debug Commands

```bash
# Local testing
npm run build
npm run preview

# Docker testing
docker build -t ider-kopi-test .
docker run -p 8080:80 ider-kopi-test

# Check container health
curl http://localhost:8080/health
```

## 📊 Monitoring

### Performance Metrics

Monitor di EasyPanel dashboard:
- Response time < 2 seconds
- Uptime > 99%
- Memory usage < 80%
- CPU usage < 70%

### Logs

```bash
# Access logs location
/var/log/nginx/access.log

# Error logs location
/var/log/nginx/error.log
```

## 🔄 Updates & Maintenance

### Deployment Updates

```bash
# Push updates
git add .
git commit -m "Update: description"
git push origin main

# EasyPanel will auto-deploy
```

### Database Migrations

```bash
# Run in Supabase SQL Editor
# Or use migration files in database/ folder
```

### Backup Strategy

1. **Database**: Supabase automatic backups
2. **Code**: Git repository
3. **Assets**: Stored in Supabase Storage

## 🔐 Security Checklist

- [x] HTTPS enabled
- [x] Security headers configured
- [x] Environment variables secured
- [x] Database RLS policies active
- [x] Rate limiting enabled
- [x] CORS properly configured

## 📈 Optimization

### Performance Tips

1. **Caching**: Static assets cached for 1 year
2. **Compression**: Gzip enabled
3. **CDN**: Use EasyPanel's built-in CDN
4. **Images**: Optimize images before upload
5. **Bundle**: Code splitting implemented

### SEO Optimization

1. **Meta tags**: Configured in index.html
2. **Sitemap**: Generate and submit
3. **Analytics**: Add Google Analytics
4. **Schema markup**: Implement structured data

## 📞 Support

### EasyPanel Support
- Documentation: https://easypanel.io/docs
- Community: Discord/Forum
- Email: <EMAIL>

### Project Support
- Repository Issues: GitHub Issues
- Documentation: This file
- Contact: Developer team

## 🎉 Success!

Jika semua langkah berhasil, website Ider Kopi sekarang live di:
- Production URL: https://your-domain.com
- Admin Panel: https://your-domain.com/admin
- Health Check: https://your-domain.com/health

**Selamat! Website Ider Kopi berhasil di-deploy ke EasyPanel! 🚀**
