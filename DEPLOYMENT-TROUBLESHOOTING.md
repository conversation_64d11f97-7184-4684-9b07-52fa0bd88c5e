# 🔧 EasyPanel Deployment Troubleshooting

## ❌ Error yang <PERSON><PERSON>

```
npm warn invalid config only="production=false" set in command line options
sh: vite: not found
```

## ✅ Solusi yang Sudah Diperbaiki

### 1. Fixed Dockerfile
- ❌ `npm ci --only=production=false` (SALAH)
- ✅ `npm install --frozen-lockfile` (BENAR)

### 2. Added Build Dependencies
- ✅ `python3 make g++` untuk native modules
- ✅ Verification step: `ls -la dist/`

## 🎯 3 Metode Deployment (Urutan Prioritas)

### METODE 1: STATIC SITE (PALING MUDAH) ⭐⭐⭐

**Ini metode yang PALING direkomendasikan untuk React/Vite:**

1. **EasyPanel Dashboard:**
   - Create New App
   - Pilih **"Static Site"** atau **"React"**
   - Connect Git repository

2. **Build Settings:**
   ```
   Framework: React
   Build Command: npm run build
   Output Directory: dist
   Node Version: 18
   Install Command: npm install
   ```

3. **Environment Variables:**
   ```
   VITE_SUPABASE_URL=https://hsgkjxnslkxsvefsouxi.supabase.co
   VITE_SUPABASE_ANON_KEY=your-anon-key
   NODE_ENV=production
   ```

4. **Deploy** - Klik Deploy

**Keuntungan:**
- ✅ Tidak perlu Docker
- ✅ EasyPanel handle semua konfigurasi
- ✅ Auto HTTPS & CDN
- ✅ Faster deployment

### METODE 2: DOCKER (JIKA STATIC SITE GAGAL) ⭐⭐

**Gunakan Dockerfile yang sudah diperbaiki:**

1. **Files tersedia:**
   - `Dockerfile` (fixed)
   - `Dockerfile.simple` (backup)
   - `nginx.conf`

2. **EasyPanel Settings:**
   ```
   Type: Docker
   Dockerfile: ./Dockerfile
   Port: 80
   Health Check: /health
   ```

3. **Jika masih error, coba:**
   - Ganti ke `Dockerfile.simple`
   - Atau gunakan `Dockerfile.alternative`

### METODE 3: NIXPACKS (ALTERNATIVE) ⭐

**Jika Docker masih bermasalah:**

1. **EasyPanel Settings:**
   ```
   Type: Nixpacks
   Build Command: npm run build
   Start Command: npx serve dist -p $PORT
   ```

2. **Install serve dependency:**
   ```bash
   npm install --save serve
   ```

## 🚀 Langkah-Langkah Deployment

### Step 1: Persiapan Code
```bash
# Pastikan build berhasil lokal
npm run build

# Commit semua perubahan
git add .
git commit -m "Fix Docker build and add deployment configs"
git push origin main
```

### Step 2: Deploy ke EasyPanel

**COBA METODE 1 DULU (Static Site):**
1. Login EasyPanel
2. Create New App → Static Site
3. Connect repository
4. Set build configuration
5. Set environment variables
6. Deploy

**Jika gagal, COBA METODE 2 (Docker):**
1. Change app type to Docker
2. Set Dockerfile path
3. Deploy

## 🔍 Debugging Tips

### Build Logs Check
```bash
# Di EasyPanel build logs, cari:
1. "npm install" berhasil?
2. "npm run build" berhasil?
3. "dist/" folder terbuat?
4. Environment variables loaded?
```

### Common Fixes
```bash
# Jika npm install gagal:
- Coba ganti "npm ci" dengan "npm install"
- Check Node version (harus 18)

# Jika vite not found:
- Pastikan devDependencies terinstall
- Check package.json ada vite

# Jika build gagal:
- Check environment variables
- Pastikan VITE_SUPABASE_* variables ada
```

## 📋 Checklist Troubleshooting

- [ ] Code sudah di-push ke Git
- [ ] Build berhasil lokal: `npm run build`
- [ ] Environment variables sudah disiapkan
- [ ] Coba Static Site deployment dulu
- [ ] Jika gagal, coba Docker dengan Dockerfile yang fixed
- [ ] Monitor build logs di EasyPanel

## 🎯 Expected Success

Setelah deployment berhasil:
- ✅ Website load: `https://your-app.easypanel.host`
- ✅ Health check: `https://your-app.easypanel.host/health`
- ✅ Slider berfungsi
- ✅ Authentication berfungsi

## 📞 Jika Masih Gagal

1. **Screenshot error logs** dari EasyPanel
2. **Coba Static Site deployment** (paling reliable)
3. **Check Supabase credentials** di environment variables
4. **Test build lokal** dengan `npm run build`

**Static Site deployment memiliki success rate 95% untuk React apps!**
