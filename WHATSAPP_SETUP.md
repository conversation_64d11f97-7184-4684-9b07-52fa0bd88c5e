# Setup WhatsApp Ordering System dengan WAHA API

## Tentang WAHA (WhatsApp HTTP API)

WAHA adalah HTTP API untuk WhatsApp yang memungkinkan Anda mengirim dan menerima pesan WhatsApp melalui HTTP requests. Ini adalah solusi self-hosted yang aman untuk integrasi WhatsApp bisnis.

## Instalasi WAHA

### 1. <PERSON><PERSON><PERSON><PERSON> Docker (Recommended)

```bash
# Pull WAHA Docker image
docker pull devlikeapro/waha

# Run WAHA container
docker run -it --rm \
  -p 3000:3000/tcp \
  --name waha \
  devlikeapro/waha

# Atau dengan persistent storage
docker run -it --rm \
  -p 3000:3000/tcp \
  -v $(pwd)/.sessions:/app/.sessions \
  --name waha \
  devlikeapro/waha
```

### 2. Menggunakan Docker Compose

Buat file `docker-compose.yml`:

```yaml
version: '3.8'
services:
  waha:
    image: devlikeapro/waha
    ports:
      - "3000:3000"
    volumes:
      - ./.sessions:/app/.sessions
    environment:
      - WAHA_PRINT_QR=true
    restart: unless-stopped
```

Jalankan dengan:
```bash
docker-compose up -d
```

## Konfigurasi

### 1. Environment Variables

Copy `.env.example` ke `.env` dan sesuaikan:

```env
# WhatsApp API WAHA Configuration
REACT_APP_WAHA_BASE_URL=http://localhost:3000
REACT_APP_WAHA_SESSION=default
REACT_APP_BUSINESS_PHONE=6281234567890

# Business Information
REACT_APP_BUSINESS_NAME=Ider Kopi Ungaran
REACT_APP_BUSINESS_ADDRESS=Jl. Raya Ungaran, Semarang
```

### 2. Setup WhatsApp Session

1. Jalankan WAHA server
2. Buka browser ke `http://localhost:3000`
3. Buat session baru:

```bash
curl -X POST http://localhost:3000/api/sessions \
  -H "Content-Type: application/json" \
  -d '{"name": "default"}'
```

4. Dapatkan QR Code untuk scan:

```bash
curl http://localhost:3000/api/sessions/default/auth/qr
```

5. Scan QR code dengan WhatsApp di HP Anda
6. Tunggu hingga status menjadi "WORKING"

## Cara Kerja Sistem Pemesanan

### 1. Flow Pemesanan

1. **Customer memilih menu** → Item ditambahkan ke keranjang
2. **Customer klik "Pesan via WhatsApp"** → Form checkout terbuka
3. **Customer isi data** → Nama, nomor HP, alamat, catatan
4. **System generate pesan** → Format pesan otomatis dengan detail pesanan
5. **Kirim via WAHA API** → Pesan dikirim ke nomor bisnis WhatsApp
6. **Fallback ke WhatsApp Web** → Jika WAHA gagal, buka WhatsApp Web

### 2. Format Pesan Otomatis

```
🛒 *PESANAN BARU - IDER KOPI*

📅 *Tanggal:* Senin, 26 Juni 2025, 14:30

👤 *Data Pelanggan:*
• Nama: John Doe
• No. HP: 081234567890
• Alamat: Jl. Merdeka No. 123, Ungaran

☕ *Detail Pesanan:*
1. Kopi Susu Ori
   2x Rp 15.000 = Rp 30.000
2. Cappuccino
   1x Rp 16.000 = Rp 16.000

💰 *Total: Rp 46.000*

📝 *Catatan:* Gula sedikit

🚚 *Status:* Menunggu Konfirmasi

Terima kasih telah memesan di Ider Kopi! 🙏
```

## API Endpoints WAHA

### Mengirim Pesan Teks

```bash
POST /api/sendText
Content-Type: application/json

{
  "session": "default",
  "chatId": "<EMAIL>",
  "text": "Hello from WAHA!"
}
```

### Cek Status Session

```bash
GET /api/sessions/default
```

### Mendapatkan QR Code

```bash
GET /api/sessions/default/auth/qr
```

## Troubleshooting

### 1. WAHA Server Tidak Bisa Diakses

- Pastikan Docker container berjalan: `docker ps`
- Cek port 3000 tidak digunakan aplikasi lain
- Restart container: `docker restart waha`

### 2. QR Code Tidak Muncul

- Pastikan session sudah dibuat
- Cek status session: `GET /api/sessions/default`
- Hapus session lama dan buat baru jika perlu

### 3. Pesan Tidak Terkirim

- Cek koneksi internet
- Pastikan nomor WhatsApp format internasional (62xxx)
- Verifikasi session masih aktif
- Cek logs WAHA container: `docker logs waha`

### 4. Fallback ke WhatsApp Web

Jika WAHA gagal, sistem otomatis akan:
1. Membuka WhatsApp Web
2. Pre-fill pesan yang sama
3. User tinggal klik send

## Keamanan

### 1. Production Setup

- Gunakan HTTPS untuk WAHA server
- Set environment variables yang aman
- Gunakan reverse proxy (nginx/traefik)
- Enable authentication jika diperlukan

### 2. Rate Limiting

WAHA memiliki built-in rate limiting untuk mencegah spam.

## Monitoring

### 1. Health Check

```bash
GET /api/sessions/default/status
```

### 2. Logs

```bash
docker logs -f waha
```

## Deployment

### 1. VPS/Server

1. Install Docker
2. Clone repository
3. Setup environment variables
4. Run dengan docker-compose
5. Setup reverse proxy untuk HTTPS

### 2. Cloud Services

WAHA bisa di-deploy ke:
- DigitalOcean Droplets
- AWS EC2
- Google Cloud Compute
- Heroku (dengan Docker)

## Support

- WAHA Documentation: https://waha.devlike.pro/
- GitHub Issues: https://github.com/devlikeapro/waha
- Discord Community: https://discord.gg/waha

## Lisensi

WAHA tersedia dalam versi:
- **Free**: Untuk development dan testing
- **Plus**: Untuk production dengan fitur tambahan
- **Pro**: Untuk enterprise dengan support penuh
