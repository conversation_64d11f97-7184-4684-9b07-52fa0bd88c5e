#!/bin/bash

# Deployment script for EasyPanel
# Usage: ./deploy.sh [environment]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default environment
ENVIRONMENT=${1:-production}

echo -e "${BLUE}🚀 Starting deployment for ${ENVIRONMENT} environment...${NC}"

# Check if required files exist
if [ ! -f "Dockerfile" ]; then
    echo -e "${YELLOW}⚠️  Dockerfile not found! Creating basic Dockerfile...${NC}"
    echo "FROM nginx:alpine
COPY dist /usr/share/nginx/html
EXPOSE 80
CMD [\"nginx\", \"-g\", \"daemon off;\"]" > Dockerfile
    echo -e "${GREEN}✅ Basic Dockerfile created${NC}"
fi

if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ package.json not found!${NC}"
    exit 1
fi

# Check if environment file exists
if [ ! -f ".env.${ENVIRONMENT}" ]; then
    echo -e "${YELLOW}⚠️  .env.${ENVIRONMENT} not found, using default configuration${NC}"
else
    echo -e "${GREEN}✅ Found .env.${ENVIRONMENT}${NC}"
    cp ".env.${ENVIRONMENT}" ".env"
fi

# Build and test locally (optional)
echo -e "${BLUE}📦 Building application locally for testing...${NC}"
npm run build

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Local build successful${NC}"
else
    echo -e "${RED}❌ Local build failed${NC}"
    exit 1
fi

# Check if Docker is available
if command -v docker &> /dev/null; then
    # Build Docker image
    echo -e "${BLUE}🐳 Building Docker image...${NC}"
    docker build -t ider-kopi-web:latest .

    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ Docker image built successfully${NC}"

        # Test Docker container locally
        echo -e "${BLUE}🧪 Testing Docker container...${NC}"
        docker run -d --name ider-kopi-test -p 8080:80 ider-kopi-web:latest

        # Wait for container to start
        sleep 5

        # Test if container is responding
        if curl -f http://localhost:8080/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Container health check passed${NC}"
            docker stop ider-kopi-test
            docker rm ider-kopi-test
        else
            echo -e "${YELLOW}⚠️  Container health check failed, but continuing...${NC}"
            docker stop ider-kopi-test
            docker rm ider-kopi-test
        fi
    else
        echo -e "${RED}❌ Docker build failed${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️  Docker not available, skipping Docker tests${NC}"
    echo -e "${BLUE}📝 Note: You can deploy directly to EasyPanel as static site${NC}"
fi

echo -e "${GREEN}🎉 Deployment preparation completed successfully!${NC}"
echo -e "${BLUE}📋 Next steps for EasyPanel deployment:${NC}"
echo -e "1. Push your code to Git repository"
echo -e "2. Connect repository to EasyPanel"
echo -e "3. Configure environment variables in EasyPanel"
echo -e "4. Deploy using EasyPanel interface"
echo -e ""
echo -e "${YELLOW}📝 Required environment variables for EasyPanel:${NC}"
echo -e "- VITE_SUPABASE_URL"
echo -e "- VITE_SUPABASE_ANON_KEY"
echo -e ""
echo -e "${GREEN}✨ Ready for deployment!${NC}"
