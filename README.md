# Ider Kopi Ungaran - Website

Website resmi Ider Kopi Ungaran dengan sistem manajemen konten dan autentikasi.

## Project info

**URL**: https://lovable.dev/projects/cbf6fa05-4fc9-4fb4-8454-5a40575d612a

## Fitur Utama

- 🏠 **Homepage** dengan informasi lengkap tentang Ider Kopi
- ☕ **Menu** kopi dan makanan dengan manajemen dinamis
- 📍 **Lokasi** dan informasi kontak
- 📝 **Blog** untuk artikel dan berita
- 🎯 **Visi & Misi** perusahaan
- 👤 **Sistem Autentikasi** dengan Supabase
- 🔐 **Admin Panel** untuk manajemen konten
- 👥 **User Management** dengan role-based access

## Sistem Autentikasi

Website ini menggunakan sistem autentikasi real yang terintegrasi dengan database Supabase:

### Role Pengguna
- **User**: Pengguna biasa yang dapat mengakses konten publik
- **Admin**: Pengguna dengan akses penuh ke admin panel

### Cara Menggunakan
1. **Daftar Akun Baru**: Kunjungi `/auth` dan buat akun baru
2. **Login**: Masuk dengan email dan password
3. **Akses Admin**: Hanya user dengan role "admin" yang dapat mengakses `/admin`

### Menjadi Admin
Untuk mempromosikan user menjadi admin, gunakan salah satu cara berikut:

**Opsi 1: Melalui Admin Panel (jika sudah ada admin)**
1. Login sebagai admin yang sudah ada
2. Buka Admin Panel → Tab "Users"
3. Masukkan email user yang ingin dipromosikan
4. Klik "Promosikan ke Admin"

**Opsi 2: Melalui Database (untuk admin pertama)**
1. Buka Supabase Dashboard → SQL Editor
2. Jalankan query: `SELECT set_admin_role('<EMAIL>');`
3. Ganti `<EMAIL>` dengan email yang ingin dijadikan admin

### Database Tables
- `profiles`: Menyimpan informasi user dan role
- `hero_slides`: Data slider background halaman depan
- `menu_items`: Data menu kopi dan makanan
- `site_content`: Konten dinamis website

## 🚀 Deployment ke EasyPanel

### Quick Start
```bash
# 1. Build dan test lokal
npm run build

# 2. Test deployment script
./deploy.sh

# 3. Push ke repository
git add .
git commit -m "Ready for deployment"
git push origin main
```

### Environment Variables (EasyPanel)
```bash
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
NODE_ENV=production
```

### Deployment Files
- `Dockerfile` - Container configuration
- `nginx.conf` - Web server configuration
- `docker-compose.yml` - Service orchestration
- `easypanel.yml` - EasyPanel configuration
- `DEPLOYMENT.md` - Detailed deployment guide

📖 **Baca [DEPLOYMENT.md](./DEPLOYMENT.md) untuk panduan lengkap deployment**

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/cbf6fa05-4fc9-4fb4-8454-5a40575d612a) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/cbf6fa05-4fc9-4fb4-8454-5a40575d612a) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
