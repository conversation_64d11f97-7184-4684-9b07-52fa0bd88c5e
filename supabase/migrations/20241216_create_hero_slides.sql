-- Migration: Create hero_slides table
-- Created: 2024-12-16
-- Description: <PERSON>bel untuk mengelola slider background halaman depan

-- Create hero_slides table
CREATE TABLE IF NOT EXISTS public.hero_slides (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    subtitle VARCHAR(255),
    description TEXT,
    image_url VARCHAR(500) NOT NULL,
    button_text VARCHAR(100),
    button_link VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_hero_slides_active_sort 
ON public.hero_slides (is_active, sort_order);

CREATE INDEX IF NOT EXISTS idx_hero_slides_created_at 
ON public.hero_slides (created_at DESC);

CREATE INDEX IF NOT EXISTS idx_hero_slides_is_active 
ON public.hero_slides (is_active);

-- Enable Row Level Security
ALTER TABLE public.hero_slides ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Allow public read access to active slides
CREATE POLICY "Allow public read access to active slides" 
ON public.hero_slides 
FOR SELECT 
USING (is_active = true);

-- Allow admin read access to all slides
CREATE POLICY "Allow admin read access to all slides" 
ON public.hero_slides 
FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
    )
);

-- Allow admin insert slides
CREATE POLICY "Allow admin insert slides" 
ON public.hero_slides 
FOR INSERT 
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
    )
);

-- Allow admin update slides
CREATE POLICY "Allow admin update slides" 
ON public.hero_slides 
FOR UPDATE 
USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
    )
);

-- Allow admin delete slides
CREATE POLICY "Allow admin delete slides" 
ON public.hero_slides 
FOR DELETE 
USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
    )
);

-- Create trigger function for auto-updating updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger
CREATE TRIGGER update_hero_slides_updated_at 
    BEFORE UPDATE ON public.hero_slides 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert default slides
INSERT INTO public.hero_slides (
    title, 
    subtitle, 
    description, 
    image_url, 
    button_text, 
    button_link, 
    is_active, 
    sort_order
) VALUES 
(
    'Ider Kopi',
    '✨ Kopi Keliling Halal #1 di Ungaran',
    'Kopi premium dengan cita rasa autentik, hadir langsung ke lokasi Anda dengan kualitas terjamin halal',
    'https://images.unsplash.com/photo-1447933601403-0c6688de566e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    '📱 Pesan Sekarang',
    '/kontak',
    true,
    1
),
(
    'Kualitas Premium',
    '🌟 Biji Kopi Pilihan Terbaik',
    'Menggunakan biji kopi robusta dan arabica dengan perbandingan 70:30 untuk cita rasa yang sempurna',
    'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    '☕ Lihat Menu',
    '/menu',
    true,
    2
),
(
    'Layanan Terpercaya',
    '🚚 Antar Langsung ke Lokasi Anda',
    'Melayani area Ungaran dan sekitarnya dengan komitmen kualitas dan kepuasan pelanggan',
    'https://images.unsplash.com/photo-1509042239860-f550ce710b93?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    '📍 Cek Lokasi',
    '/lokasi',
    true,
    3
)
ON CONFLICT (id) DO NOTHING;
