-- =====================================================
-- HERO SLIDES TABLE SCHEMA
-- =====================================================
-- Tabel untuk menyimpan data slider background halaman depan
-- Digunakan untuk mengelola slide dinamis dengan gambar, text, dan CTA

-- 1. CREATE TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS public.hero_slides (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    subtitle VARCHAR(255),
    description TEXT,
    image_url VARCHAR(500) NOT NULL,
    button_text VARCHAR(100),
    button_link VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. CREATE INDEXES
-- =====================================================
-- Index untuk query berdasarkan status aktif dan urutan
CREATE INDEX IF NOT EXISTS idx_hero_slides_active_sort 
ON public.hero_slides (is_active, sort_order);

-- Index untuk query berdasarkan tanggal pembuatan
CREATE INDEX IF NOT EXISTS idx_hero_slides_created_at 
ON public.hero_slides (created_at DESC);

-- Index untuk query berdasarkan status aktif
CREATE INDEX IF NOT EXISTS idx_hero_slides_is_active 
ON public.hero_slides (is_active);

-- 3. ROW LEVEL SECURITY (RLS)
-- =====================================================
-- Enable RLS untuk keamanan
ALTER TABLE public.hero_slides ENABLE ROW LEVEL SECURITY;

-- Policy untuk SELECT - semua user bisa melihat slide aktif
CREATE POLICY "Allow public read access to active slides" 
ON public.hero_slides 
FOR SELECT 
USING (is_active = true);

-- Policy untuk SELECT - admin bisa melihat semua slide
CREATE POLICY "Allow admin read access to all slides" 
ON public.hero_slides 
FOR SELECT 
USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
    )
);

-- Policy untuk INSERT - hanya admin yang bisa menambah slide
CREATE POLICY "Allow admin insert slides" 
ON public.hero_slides 
FOR INSERT 
WITH CHECK (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
    )
);

-- Policy untuk UPDATE - hanya admin yang bisa mengupdate slide
CREATE POLICY "Allow admin update slides" 
ON public.hero_slides 
FOR UPDATE 
USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
    )
);

-- Policy untuk DELETE - hanya admin yang bisa menghapus slide
CREATE POLICY "Allow admin delete slides" 
ON public.hero_slides 
FOR DELETE 
USING (
    EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
    )
);

-- 4. TRIGGERS
-- =====================================================
-- Trigger untuk auto-update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_hero_slides_updated_at 
    BEFORE UPDATE ON public.hero_slides 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- 5. SAMPLE DATA
-- =====================================================
-- Insert sample slides (opsional - untuk testing)
INSERT INTO public.hero_slides (
    title, 
    subtitle, 
    description, 
    image_url, 
    button_text, 
    button_link, 
    is_active, 
    sort_order
) VALUES 
(
    'Ider Kopi',
    '✨ Kopi Keliling Halal #1 di Ungaran',
    'Kopi premium dengan cita rasa autentik, hadir langsung ke lokasi Anda dengan kualitas terjamin halal',
    'https://images.unsplash.com/photo-1447933601403-0c6688de566e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    '📱 Pesan Sekarang',
    '/kontak',
    true,
    1
),
(
    'Kualitas Premium',
    '🌟 Biji Kopi Pilihan Terbaik',
    'Menggunakan biji kopi robusta dan arabica dengan perbandingan 70:30 untuk cita rasa yang sempurna',
    'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    '☕ Lihat Menu',
    '/menu',
    true,
    2
),
(
    'Layanan Terpercaya',
    '🚚 Antar Langsung ke Lokasi Anda',
    'Melayani area Ungaran dan sekitarnya dengan komitmen kualitas dan kepuasan pelanggan',
    'https://images.unsplash.com/photo-1509042239860-f550ce710b93?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    '📍 Cek Lokasi',
    '/lokasi',
    true,
    3
)
ON CONFLICT (id) DO NOTHING;

-- 6. USEFUL QUERIES
-- =====================================================

-- Query untuk mendapatkan semua slide aktif berurutan
-- SELECT * FROM public.hero_slides 
-- WHERE is_active = true 
-- ORDER BY sort_order ASC;

-- Query untuk mendapatkan slide untuk admin (semua slide)
-- SELECT * FROM public.hero_slides 
-- ORDER BY sort_order ASC, created_at DESC;

-- Query untuk mengaktifkan/nonaktifkan slide
-- UPDATE public.hero_slides 
-- SET is_active = NOT is_active, updated_at = NOW() 
-- WHERE id = 'slide-uuid-here';

-- Query untuk mengubah urutan slide
-- UPDATE public.hero_slides 
-- SET sort_order = new_order, updated_at = NOW() 
-- WHERE id = 'slide-uuid-here';

-- Query untuk menghitung total slide aktif
-- SELECT COUNT(*) as total_active_slides 
-- FROM public.hero_slides 
-- WHERE is_active = true;

-- Query untuk mendapatkan slide terbaru
-- SELECT * FROM public.hero_slides 
-- ORDER BY created_at DESC 
-- LIMIT 5;

-- 7. MAINTENANCE QUERIES
-- =====================================================

-- Reset sort_order untuk semua slide (maintenance)
-- WITH ordered_slides AS (
--     SELECT id, ROW_NUMBER() OVER (ORDER BY sort_order, created_at) as new_order
--     FROM public.hero_slides
-- )
-- UPDATE public.hero_slides 
-- SET sort_order = ordered_slides.new_order
-- FROM ordered_slides 
-- WHERE public.hero_slides.id = ordered_slides.id;

-- Backup slides ke tabel temporary
-- CREATE TABLE hero_slides_backup AS 
-- SELECT * FROM public.hero_slides;

-- Restore dari backup
-- INSERT INTO public.hero_slides 
-- SELECT * FROM hero_slides_backup 
-- ON CONFLICT (id) DO NOTHING;

-- =====================================================
-- NOTES:
-- =====================================================
-- 1. Tabel ini menggunakan UUID sebagai primary key untuk keamanan
-- 2. RLS policies memastikan hanya admin yang bisa CRUD
-- 3. Public user hanya bisa melihat slide yang aktif
-- 4. Trigger otomatis update timestamp saat ada perubahan
-- 5. Index dioptimalkan untuk query yang sering digunakan
-- 6. Sample data disediakan untuk testing awal
-- =====================================================
