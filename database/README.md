# Hero Slides Database Schema

Dokumentasi lengkap untuk tabel `hero_slides` yang digunakan untuk mengelola slider background halaman depan website Ider Kopi.

## 📋 Table Structure

### `public.hero_slides`

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| `id` | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique identifier untuk setiap slide |
| `title` | VARCHAR(255) | NOT NULL | Judul utama slide yang ditampilkan |
| `subtitle` | VARCHAR(255) | NULLABLE | Subtitle atau badge text (opsional) |
| `description` | TEXT | NULLABLE | Deskripsi lengkap slide (opsional) |
| `image_url` | VARCHAR(500) | NOT NULL | URL gambar background slide |
| `button_text` | VARCHAR(100) | NULLABLE | Text untuk tombol CTA (opsional) |
| `button_link` | VARCHAR(500) | NULLABLE | Link tujuan tombol CTA (opsional) |
| `is_active` | BOOLEAN | DEFAULT true | Status aktif/nonaktif slide |
| `sort_order` | INTEGER | DEFAULT 1 | Urutan tampil slide |
| `created_at` | TIMESTAMP WITH TIME ZONE | DEFAULT NOW() | Waktu pembuatan record |
| `updated_at` | TIMESTAMP WITH TIME ZONE | DEFAULT NOW() | Waktu update terakhir |

## 🔍 Indexes

1. **`idx_hero_slides_active_sort`** - Composite index pada `(is_active, sort_order)`
   - Optimasi untuk query slide aktif berurutan
   
2. **`idx_hero_slides_created_at`** - Index pada `created_at DESC`
   - Optimasi untuk query slide terbaru
   
3. **`idx_hero_slides_is_active`** - Index pada `is_active`
   - Optimasi untuk filter berdasarkan status

## 🔒 Row Level Security (RLS)

### Policies

1. **`Allow public read access to active slides`**
   - **Type**: SELECT
   - **Target**: Public users
   - **Rule**: Hanya slide dengan `is_active = true`

2. **`Allow admin read access to all slides`**
   - **Type**: SELECT  
   - **Target**: Admin users
   - **Rule**: Semua slide (aktif dan nonaktif)

3. **`Allow admin insert slides`**
   - **Type**: INSERT
   - **Target**: Admin users only
   - **Rule**: Hanya user dengan role 'admin'

4. **`Allow admin update slides`**
   - **Type**: UPDATE
   - **Target**: Admin users only
   - **Rule**: Hanya user dengan role 'admin'

5. **`Allow admin delete slides`**
   - **Type**: DELETE
   - **Target**: Admin users only
   - **Rule**: Hanya user dengan role 'admin'

## ⚡ Triggers

### `update_hero_slides_updated_at`
- **Type**: BEFORE UPDATE
- **Function**: `update_updated_at_column()`
- **Purpose**: Otomatis update field `updated_at` saat ada perubahan

## 📁 Files

### 1. `hero_slides_schema.sql`
File lengkap dengan:
- CREATE TABLE statement
- Indexes
- RLS policies
- Triggers
- Sample data
- Dokumentasi query

### 2. `supabase/migrations/20241216_create_hero_slides.sql`
File migrasi Supabase yang bisa dijalankan langsung:
```bash
supabase db push
```

### 3. `hero_slides_queries.sql`
Kumpulan query berguna untuk:
- CRUD operations
- Maintenance
- Analytics
- Backup & restore
- Development

## 🚀 Usage Examples

### Frontend (Public Access)
```sql
-- Mendapatkan slide aktif untuk ditampilkan
SELECT id, title, subtitle, description, image_url, button_text, button_link
FROM public.hero_slides 
WHERE is_active = true 
ORDER BY sort_order ASC;
```

### Admin Panel
```sql
-- Mendapatkan semua slide untuk admin
SELECT * FROM public.hero_slides 
ORDER BY sort_order ASC, created_at DESC;

-- Menambah slide baru
INSERT INTO public.hero_slides (title, image_url, sort_order) 
VALUES ('New Slide', 'https://example.com/image.jpg', 1);

-- Update slide
UPDATE public.hero_slides 
SET title = 'Updated Title', updated_at = NOW()
WHERE id = 'slide-uuid';

-- Toggle status
UPDATE public.hero_slides 
SET is_active = NOT is_active, updated_at = NOW()
WHERE id = 'slide-uuid';
```

## 🔧 Installation

### Option 1: Manual SQL
```sql
-- Jalankan file schema lengkap
\i database/hero_slides_schema.sql
```

### Option 2: Supabase Migration
```bash
# Copy migration file ke folder supabase/migrations/
cp database/hero_slides_schema.sql supabase/migrations/20241216_create_hero_slides.sql

# Push ke database
supabase db push
```

### Option 3: Supabase Dashboard
1. Buka Supabase Dashboard
2. Go to SQL Editor
3. Copy-paste isi file `hero_slides_schema.sql`
4. Run query

## 📊 Sample Data

Tabel akan otomatis terisi dengan 3 slide default:

1. **Slide 1**: "Ider Kopi" - Intro utama
2. **Slide 2**: "Kualitas Premium" - Fokus produk  
3. **Slide 3**: "Layanan Terpercaya" - Fokus service

## 🛠 Maintenance

### Reset Sort Order
```sql
WITH ordered_slides AS (
    SELECT id, ROW_NUMBER() OVER (ORDER BY sort_order, created_at) as new_order
    FROM public.hero_slides
)
UPDATE public.hero_slides 
SET sort_order = ordered_slides.new_order, updated_at = NOW()
FROM ordered_slides 
WHERE public.hero_slides.id = ordered_slides.id;
```

### Backup Data
```sql
CREATE TABLE hero_slides_backup AS 
SELECT * FROM public.hero_slides;
```

### Analytics
```sql
-- Statistik slide
SELECT 
    COUNT(*) as total,
    COUNT(*) FILTER (WHERE is_active = true) as active,
    COUNT(*) FILTER (WHERE is_active = false) as inactive
FROM public.hero_slides;
```

## 🔐 Security Notes

1. **RLS Enabled**: Semua akses data dikontrol oleh Row Level Security
2. **Admin Only**: Hanya admin yang bisa CRUD slides
3. **Public Read**: User biasa hanya bisa lihat slide aktif
4. **UUID Primary Key**: Menggunakan UUID untuk keamanan
5. **Input Validation**: Constraint pada level database

## 🎯 Performance Tips

1. **Index Usage**: Query selalu gunakan index yang tersedia
2. **Limit Results**: Gunakan LIMIT untuk query besar
3. **Specific WHERE**: Hindari full table scan
4. **Monitor Queries**: Gunakan EXPLAIN ANALYZE

## 📝 Notes

- Tabel ini terintegrasi dengan sistem autentikasi existing
- Mendukung soft delete melalui field `is_active`
- Optimized untuk query frontend yang sering diakses
- Compatible dengan TypeScript types yang sudah didefinisikan
- Mendukung real-time updates melalui Supabase subscriptions
