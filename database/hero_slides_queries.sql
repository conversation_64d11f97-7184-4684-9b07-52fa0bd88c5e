-- =====================================================
-- HERO SLIDES - USEFUL QUERIES
-- =====================================================
-- Kumpulan query berguna untuk mengelola tabel hero_slides

-- =====================================================
-- 1. BASIC QUERIES
-- =====================================================

-- Mendapatkan semua slide aktif untuk frontend (berurutan)
SELECT 
    id,
    title,
    subtitle,
    description,
    image_url,
    button_text,
    button_link,
    sort_order
FROM public.hero_slides 
WHERE is_active = true 
ORDER BY sort_order ASC;

-- Mendapatkan semua slide untuk admin panel
SELECT 
    id,
    title,
    subtitle,
    description,
    image_url,
    button_text,
    button_link,
    is_active,
    sort_order,
    created_at,
    updated_at
FROM public.hero_slides 
ORDER BY sort_order ASC, created_at DESC;

-- Mendapatkan slide berdasarkan ID
SELECT * FROM public.hero_slides 
WHERE id = 'your-slide-uuid-here';

-- =====================================================
-- 2. INSERT QUERIES
-- =====================================================

-- Menambah slide baru
INSERT INTO public.hero_slides (
    title,
    subtitle,
    description,
    image_url,
    button_text,
    button_link,
    is_active,
    sort_order
) VALUES (
    'Judul Slide Baru',
    'Subtitle Slide',
    'Deskripsi lengkap slide ini',
    'https://example.com/image.jpg',
    'Tombol CTA',
    '/link-tujuan',
    true,
    (SELECT COALESCE(MAX(sort_order), 0) + 1 FROM public.hero_slides)
);

-- =====================================================
-- 3. UPDATE QUERIES
-- =====================================================

-- Update informasi slide
UPDATE public.hero_slides 
SET 
    title = 'Judul Baru',
    subtitle = 'Subtitle Baru',
    description = 'Deskripsi baru',
    image_url = 'https://example.com/new-image.jpg',
    button_text = 'Tombol Baru',
    button_link = '/link-baru',
    updated_at = NOW()
WHERE id = 'your-slide-uuid-here';

-- Toggle status aktif/nonaktif slide
UPDATE public.hero_slides 
SET 
    is_active = NOT is_active,
    updated_at = NOW()
WHERE id = 'your-slide-uuid-here';

-- Mengaktifkan slide
UPDATE public.hero_slides 
SET 
    is_active = true,
    updated_at = NOW()
WHERE id = 'your-slide-uuid-here';

-- Menonaktifkan slide
UPDATE public.hero_slides 
SET 
    is_active = false,
    updated_at = NOW()
WHERE id = 'your-slide-uuid-here';

-- Update urutan slide
UPDATE public.hero_slides 
SET 
    sort_order = 1,
    updated_at = NOW()
WHERE id = 'your-slide-uuid-here';

-- =====================================================
-- 4. DELETE QUERIES
-- =====================================================

-- Hapus slide berdasarkan ID
DELETE FROM public.hero_slides 
WHERE id = 'your-slide-uuid-here';

-- Hapus semua slide nonaktif (hati-hati!)
-- DELETE FROM public.hero_slides 
-- WHERE is_active = false;

-- =====================================================
-- 5. MAINTENANCE QUERIES
-- =====================================================

-- Reset urutan slide (1, 2, 3, dst)
WITH ordered_slides AS (
    SELECT 
        id, 
        ROW_NUMBER() OVER (ORDER BY sort_order, created_at) as new_order
    FROM public.hero_slides
)
UPDATE public.hero_slides 
SET 
    sort_order = ordered_slides.new_order,
    updated_at = NOW()
FROM ordered_slides 
WHERE public.hero_slides.id = ordered_slides.id;

-- Pindahkan slide ke urutan tertentu
UPDATE public.hero_slides 
SET sort_order = 999 
WHERE id = 'slide-to-move';

UPDATE public.hero_slides 
SET sort_order = sort_order + 1 
WHERE sort_order >= 2 AND id != 'slide-to-move';

UPDATE public.hero_slides 
SET sort_order = 2 
WHERE id = 'slide-to-move';

-- =====================================================
-- 6. ANALYTICS QUERIES
-- =====================================================

-- Hitung total slide
SELECT COUNT(*) as total_slides FROM public.hero_slides;

-- Hitung slide aktif vs nonaktif
SELECT 
    is_active,
    COUNT(*) as count
FROM public.hero_slides 
GROUP BY is_active;

-- Slide terbaru
SELECT 
    title,
    created_at,
    is_active
FROM public.hero_slides 
ORDER BY created_at DESC 
LIMIT 5;

-- Slide yang paling lama tidak diupdate
SELECT 
    title,
    updated_at,
    is_active
FROM public.hero_slides 
ORDER BY updated_at ASC 
LIMIT 5;

-- =====================================================
-- 7. VALIDATION QUERIES
-- =====================================================

-- Cek slide dengan URL gambar yang rusak (contoh)
SELECT 
    id,
    title,
    image_url
FROM public.hero_slides 
WHERE image_url IS NULL OR image_url = '';

-- Cek slide tanpa title
SELECT 
    id,
    title,
    created_at
FROM public.hero_slides 
WHERE title IS NULL OR title = '';

-- Cek duplikasi sort_order
SELECT 
    sort_order,
    COUNT(*) as count
FROM public.hero_slides 
GROUP BY sort_order 
HAVING COUNT(*) > 1;

-- =====================================================
-- 8. BACKUP & RESTORE QUERIES
-- =====================================================

-- Backup slides ke tabel temporary
CREATE TABLE hero_slides_backup AS 
SELECT * FROM public.hero_slides;

-- Restore dari backup
INSERT INTO public.hero_slides 
SELECT * FROM hero_slides_backup 
ON CONFLICT (id) DO NOTHING;

-- Export slides sebagai JSON (untuk backup)
SELECT json_agg(
    json_build_object(
        'id', id,
        'title', title,
        'subtitle', subtitle,
        'description', description,
        'image_url', image_url,
        'button_text', button_text,
        'button_link', button_link,
        'is_active', is_active,
        'sort_order', sort_order,
        'created_at', created_at,
        'updated_at', updated_at
    )
) as slides_backup
FROM public.hero_slides;

-- =====================================================
-- 9. PERFORMANCE QUERIES
-- =====================================================

-- Cek penggunaan index
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM public.hero_slides 
WHERE is_active = true 
ORDER BY sort_order;

-- Statistik tabel
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE tablename = 'hero_slides';

-- =====================================================
-- 10. DEVELOPMENT QUERIES
-- =====================================================

-- Reset ke data default (development only)
TRUNCATE public.hero_slides RESTART IDENTITY CASCADE;

-- Insert data testing
INSERT INTO public.hero_slides (title, image_url, is_active, sort_order) VALUES
('Test Slide 1', 'https://picsum.photos/1920/1080?random=1', true, 1),
('Test Slide 2', 'https://picsum.photos/1920/1080?random=2', true, 2),
('Test Slide 3', 'https://picsum.photos/1920/1080?random=3', false, 3);

-- =====================================================
-- NOTES:
-- =====================================================
-- 1. Selalu backup data sebelum menjalankan query DELETE atau UPDATE massal
-- 2. Gunakan WHERE clause yang spesifik untuk menghindari perubahan yang tidak diinginkan
-- 3. Test query di environment development sebelum production
-- 4. Monitor performance query dengan EXPLAIN ANALYZE
-- 5. Pastikan RLS policies sesuai dengan kebutuhan keamanan
-- =====================================================
