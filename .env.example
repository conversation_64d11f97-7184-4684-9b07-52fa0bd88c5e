# Environment Variables Template for Ider Kopi Website
# Copy this file to .env and fill in your actual values

# Supabase Configuration (REQUIRED)
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# Application Configuration
VITE_APP_TITLE=Ider Kopi Ungaran
VITE_APP_DESCRIPTION=Kopi Keliling Halal #1 di Ungaran

# Build Environment
NODE_ENV=production

# Optional: API Configuration
VITE_API_URL=https://your-api-domain.com

# Optional: Analytics
VITE_GA_TRACKING_ID=your-google-analytics-id

# Optional: Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_PWA=true

# WhatsApp API WAHA Configuration
REACT_APP_WAHA_BASE_URL=http://localhost:3000
REACT_APP_WAHA_SESSION=default
REACT_APP_BUSINESS_PHONE=6281234567890

# Business Information
REACT_APP_BUSINESS_NAME=Ider Kopi Ungaran
REACT_APP_BUSINESS_ADDRESS=Jl. Raya Ungaran, Semarang

# EasyPanel Deployment Notes:
# 1. Set these variables in EasyPanel Dashboard > App Settings > Environment
# 2. Do NOT include quotes around values in EasyPanel
# 3. VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY are REQUIRED
# 4. Get Supabase credentials from: https://app.supabase.com/project/your-project/settings/api
