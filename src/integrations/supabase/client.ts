// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://hsgkjxnslkxsvefsouxi.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhzZ2tqeG5zbGt4c3ZlZnNvdXhpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5Njc1MDQsImV4cCI6MjA2NTU0MzUwNH0.hfZJyEPfbinAQR5PCqPzAimu9bUGAuDxqmaOaRnKCY0";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);