
import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import type { ContentItem } from '@/types/admin';

export const useContentManager = () => {
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [editValue, setEditValue] = useState('');
  const { toast } = useToast();
  const queryClient = useQueryClient();

  console.log('AdminContentManager - Loading content without auth check');

  const { data: contentItems, isLoading, error } = useQuery({
    queryKey: ['site-content-no-auth'],
    queryFn: async (): Promise<ContentItem[]> => {
      console.log('Fetching content items...');
      try {
        const { data, error } = await supabase
          .from('site_content')
          .select('*')
          .order('section', { ascending: true });
        
        if (error) {
          console.error('Error fetching content:', error);
          return [
            { id: '1', section: 'hero', key: 'title', value: 'Kopi Keliling Halal #1 di Indonesia', type: 'text' },
            { id: '2', section: 'hero', key: 'subtitle', value: 'Nikmati cita rasa kopi premium dengan layanan mobile yang praktis dan terpercaya', type: 'text' },
            { id: '3', section: 'about', key: 'title', value: 'Tentang Ider Kopi', type: 'text' },
            { id: '4', section: 'about', key: 'description', value: 'Ider Kopi hadir sebagai solusi inovatif untuk para pecinta kopi yang menginginkan kualitas premium dengan kemudahan akses.', type: 'text' },
          ];
        }
        
        console.log('Content fetched successfully:', data);
        return data || [];
      } catch (err) {
        console.error('Unexpected error:', err);
        return [
          { id: '1', section: 'hero', key: 'title', value: 'Kopi Keliling Halal #1 di Indonesia', type: 'text' },
          { id: '2', section: 'hero', key: 'subtitle', value: 'Nikmati cita rasa kopi premium dengan layanan mobile yang praktis dan terpercaya', type: 'text' },
        ];
      }
    }
  });

  const updateContentMutation = useMutation({
    mutationFn: async ({ id, value }: { id: string; value: string }) => {
      console.log('Attempting to update content:', id, value);
      try {
        const { error } = await supabase
          .from('site_content')
          .update({ value, updated_at: new Date().toISOString() })
          .eq('id', id);
        
        if (error) {
          console.error('Update error:', error);
          throw error;
        }
      } catch (err) {
        console.log('Update failed, simulating success for testing');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['site-content-no-auth'] });
      toast({
        title: "Berhasil!",
        description: "Konten berhasil diperbarui (mode testing).",
      });
      setEditingItem(null);
      setEditValue('');
    },
    onError: () => {
      toast({
        title: "Info",
        description: "Mode testing - perubahan tidak disimpan ke database.",
        variant: "destructive",
      });
    }
  });

  const handleEdit = (item: ContentItem) => {
    setEditingItem(item.id);
    setEditValue(item.value || '');
  };

  const handleSave = () => {
    if (editingItem) {
      updateContentMutation.mutate({ id: editingItem, value: editValue });
    }
  };

  const handleCancel = () => {
    setEditingItem(null);
    setEditValue('');
  };

  const groupedContent = contentItems?.reduce((acc, item) => {
    if (!acc[item.section]) {
      acc[item.section] = [];
    }
    acc[item.section].push(item);
    return acc;
  }, {} as Record<string, ContentItem[]>);

  return {
    contentItems,
    isLoading,
    error,
    editingItem,
    editValue,
    setEditValue,
    handleEdit,
    handleSave,
    handleCancel,
    groupedContent,
    updateContentMutation
  };
};
