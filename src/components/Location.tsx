
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export const Location = () => {
  return (
    <section className="py-20 px-4 bg-gradient-to-r from-red-100 to-rose-100">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold text-red-900 mb-4">
            Keliling di Ungaran & Sekitarnya
          </h2>
          <div className="w-24 h-1 bg-red-500 mx-auto mb-6"></div>
          <p className="text-xl text-red-700 max-w-3xl mx-auto">
            Hingga kini, kami telah hadir di sekolah, perkantoran, & area publik
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div className="space-y-6">
            <Card className="border-none shadow-lg bg-white/90 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xl">🏫</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-red-900 mb-1">SMANSA Ungaran</h3>
                    <p className="text-red-700">Baru masuk kantin SMANSA Ungaran</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-none shadow-lg bg-white/90 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xl">🏢</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-red-900 mb-1">Area Perkantoran</h3>
                    <p className="text-red-700">Melayani area kantor di Ungaran & sekitar</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-none shadow-lg bg-white/90 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xl">🏪</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-red-900 mb-1">Area Publik</h3>
                    <p className="text-red-700">Taman, terminal, dan ruang publik lainnya</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="bg-gradient-to-r from-red-500 to-rose-500 rounded-lg p-6 text-center">
              <h3 className="text-2xl font-bold text-white mb-3">Cakupan Area Kami</h3>
              <div className="flex flex-wrap justify-center gap-3 mb-4">
                <span className="bg-white/20 px-3 py-1 rounded-full text-white font-medium">Ungaran</span>
                <span className="bg-white/20 px-3 py-1 rounded-full text-white font-medium">Ambarawa</span>
                <span className="bg-white/20 px-3 py-1 rounded-full text-white font-medium">Tembalang</span>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-2xl">
              <div className="text-center">
                <div className="w-32 h-32 bg-gradient-to-br from-red-400 to-rose-500 rounded-full mx-auto mb-6 flex items-center justify-center">
                  <span className="text-5xl">🚲</span>
                </div>
                <h3 className="text-2xl font-bold text-red-900 mb-4">Kami Datang ke Kamu!</h3>
                <p className="text-red-700 mb-6 leading-relaxed">
                  Tidak perlu repot keluar rumah atau kantor. Tim Ider Kopi siap mengantarkan 
                  kopi segar langsung ke lokasi Anda dengan pelayanan terbaik.
                </p>
                <Button className="bg-gradient-to-r from-red-600 to-rose-600 hover:from-red-500 hover:to-rose-500 text-white font-semibold px-6 py-3">
                  Panggil Ider Kopi Sekarang
                </Button>
              </div>
            </div>

            {/* Decorative elements */}
            <div className="absolute -top-6 -right-6 w-20 h-20 bg-red-300 rounded-full opacity-20 animate-bounce"></div>
            <div className="absolute -bottom-6 -left-6 w-16 h-16 bg-rose-300 rounded-full opacity-30 animate-bounce" style={{animationDelay: '0.5s'}}></div>
          </div>
        </div>
      </div>
    </section>
  );
};
