
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

export const Features = () => {
  const features = [
    {
      icon: "✅",
      title: "Halal Bersertifikat",
      description: "<PERSON><PERSON><PERSON> resmi (ID33310022698060625) dengan jaminan halal",
      gradient: "from-green-400 to-emerald-500"
    },
    {
      icon: "☕",
      title: "Tanpa SKM",
      description: "Hanya biji kopi asli lokal, tanpa bahan pengawet",
      gradient: "from-red-400 to-rose-500"
    },
    {
      icon: "💰",
      title: "Harga Bersahabat",
      description: "Semua menu premium hanya Rp 15.000",
      gradient: "from-red-500 to-red-600"
    },
    {
      icon: "🚲",
      title: "Mudah Dijangkau",
      description: "<PERSON><PERSON> keliling langsung ke lokasimu",
      gradient: "from-red-600 to-red-700"
    },
    {
      icon: "🏆",
      title: "Quality First",
      description: "Campuran handmade 70:30 untuk rasa balance & aromatik",
      gradient: "from-rose-400 to-red-500"
    }
  ];

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-rose-50 to-red-50 dark:from-gray-900 dark:to-gray-800">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold text-red-900 dark:text-red-300 mb-4">
            Kenapa Pilih Ider Kopi?
          </h2>
          <div className="w-24 h-1 bg-red-500 dark:bg-red-400 mx-auto mb-6"></div>
          <p className="text-xl text-red-700 dark:text-red-300">
            Lima alasan utama yang membuat Ider Kopi istimewa
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 border-none bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm overflow-hidden relative">
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500`} />
              <CardContent className="p-8 text-center relative z-10">
                <div className="mb-6">
                  <div className={`w-20 h-20 mx-auto rounded-full bg-gradient-to-br ${feature.gradient} flex items-center justify-center text-3xl mb-4 transform group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                    {feature.icon}
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-red-900 dark:text-red-300 mb-4 group-hover:text-red-700 dark:group-hover:text-red-200 transition-colors">
                  {feature.title}
                </h3>
                <p className="text-red-700 dark:text-red-300 leading-relaxed group-hover:text-red-600 dark:group-hover:text-red-200 transition-colors">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};
