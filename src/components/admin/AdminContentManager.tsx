
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { ContentSection } from './ContentSection';
import { useContentManager } from '@/hooks/useContentManager';

export const AdminContentManager = () => {
  const {
    isLoading,
    error,
    editingItem,
    editValue,
    setEditValue,
    handleEdit,
    handleSave,
    handleCancel,
    groupedContent,
    updateContentMutation
  } = useContentManager();

  console.log('AdminContentManager - Current state:', {
    isLoading,
    error,
    editingItem,
    groupedContent: Object.keys(groupedContent || {})
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
      </div>
    );
  }

  if (error) {
    console.error('Content loading error:', error);
    return (
      <div className="text-center py-12">
        <p className="text-red-600 dark:text-red-400">
          Error loading content. Using demo data for testing.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="border-none shadow-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-red-900 dark:text-red-100">Manajemen Konten Website</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-700 dark:text-red-300 mb-6">
            Edit konten yang ditampilkan di berbagai bagian website. (Mode Testing - No Auth)
          </p>

          {groupedContent && Object.keys(groupedContent).length > 0 ? (
            Object.entries(groupedContent).map(([section, items]) => (
              <ContentSection
                key={section}
                section={section}
                items={items}
                editingItem={editingItem}
                editValue={editValue}
                onEditValueChange={setEditValue}
                onEdit={handleEdit}
                onSave={handleSave}
                onCancel={handleCancel}
                isSaving={updateContentMutation.isPending}
              />
            ))
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">
                Tidak ada konten yang ditemukan. Menggunakan data demo untuk testing.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
