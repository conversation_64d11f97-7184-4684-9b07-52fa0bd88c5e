
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { ContentItemCard } from './ContentItemCard';
import type { ContentItem } from '@/types/admin';

interface ContentSectionProps {
  section: string;
  items: ContentItem[];
  editingItem: string | null;
  editValue: string;
  onEditValueChange: (value: string) => void;
  onEdit: (item: ContentItem) => void;
  onSave: () => void;
  onCancel: () => void;
  isSaving: boolean;
}

const getSectionTitle = (section: string) => {
  switch (section) {
    case 'hero':
      return 'Halaman Utama';
    case 'about':
      return 'Tentang Kami';
    case 'contact':
      return 'Kontak';
    default:
      return section;
  }
};

export const ContentSection = ({
  section,
  items,
  editingItem,
  editValue,
  onEditValueChange,
  onEdit,
  onSave,
  onCancel,
  isSaving
}: ContentSectionProps) => {
  return (
    <Card className="mb-6 border border-red-200 dark:border-red-800">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg text-red-800 dark:text-red-200 capitalize">
          {getSectionTitle(section)}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {items.map((item) => (
          <ContentItemCard
            key={item.id}
            item={item}
            isEditing={editingItem === item.id}
            editValue={editValue}
            onEditValueChange={onEditValueChange}
            onEdit={() => onEdit(item)}
            onSave={onSave}
            onCancel={onCancel}
            isSaving={isSaving}
          />
        ))}
      </CardContent>
    </Card>
  );
};
