
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Pencil, Save, X, Plus, Trash2, Calendar, User } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  category: string;
  author: string;
  date: string;
  featured: boolean;
}

export const AdminBlogManager = () => {
  const { toast } = useToast();
  
  // Demo data - in real app this would come from database
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([
    {
      id: '1',
      title: '<PERSON><PERSON><PERSON><PERSON><PERSON> Lokal Bersama <PERSON>',
      excerpt: 'Mengenal lebih dekat perjalanan petani kopi dari Jawa <PERSON>at yang bergabung dengan program kemitraan I<PERSON>.',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
      category: 'Kemitraan',
      author: 'Admin Ider',
      date: '2024-01-15',
      featured: true
    },
    {
      id: '2',
      title: 'Tips Menyeduh Kopi yang Sempurna di Rumah',
      excerpt: 'Panduan lengkap untuk menghasilkan secangkir kopi berkualitas tinggi dengan alat sederhana di rumah.',
      content: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
      category: 'Tips & Trik',
      author: 'Barista Ider',
      date: '2024-01-10',
      featured: false
    }
  ]);

  const [editingPost, setEditingPost] = useState<string | null>(null);
  const [isAddingPost, setIsAddingPost] = useState(false);
  const [editForm, setEditForm] = useState({
    title: '',
    excerpt: '',
    content: '',
    category: '',
    author: '',
    featured: false
  });

  const categories = ['Berita', 'Tips & Trik', 'Kemitraan', 'Produk', 'Event'];

  const handleEditPost = (post: BlogPost) => {
    setEditingPost(post.id);
    setEditForm({
      title: post.title,
      excerpt: post.excerpt,
      content: post.content,
      category: post.category,
      author: post.author,
      featured: post.featured
    });
  };

  const handleSavePost = () => {
    if (editingPost) {
      setBlogPosts(prev => prev.map(post => 
        post.id === editingPost 
          ? { ...post, ...editForm }
          : post
      ));
      setEditingPost(null);
      resetForm();
      toast({
        title: "Berhasil!",
        description: "Artikel berhasil diperbarui.",
      });
    }
  };

  const handleAddPost = () => {
    const newPost: BlogPost = {
      id: Date.now().toString(),
      ...editForm,
      date: new Date().toISOString().split('T')[0]
    };
    setBlogPosts(prev => [newPost, ...prev]);
    setIsAddingPost(false);
    resetForm();
    toast({
      title: "Berhasil!",
      description: "Artikel baru berhasil ditambahkan.",
    });
  };

  const handleDeletePost = (id: string) => {
    setBlogPosts(prev => prev.filter(post => post.id !== id));
    toast({
      title: "Berhasil!",
      description: "Artikel berhasil dihapus.",
    });
  };

  const resetForm = () => {
    setEditForm({
      title: '',
      excerpt: '',
      content: '',
      category: '',
      author: '',
      featured: false
    });
  };

  const handleCancel = () => {
    setEditingPost(null);
    setIsAddingPost(false);
    resetForm();
  };

  return (
    <div className="space-y-6">
      <Card className="border-none shadow-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="text-red-900 dark:text-red-100">Manajemen Blog</CardTitle>
            <Button
              onClick={() => setIsAddingPost(true)}
              className="bg-red-600 hover:bg-red-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              Tambah Artikel
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <p className="text-red-700 dark:text-red-300 mb-6">
            Kelola artikel blog dan konten berita untuk website. (Mode Testing - No Auth)
          </p>

          {/* Add New Post Form */}
          {isAddingPost && (
            <Card className="mb-6 border border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/10">
              <CardHeader>
                <CardTitle className="text-lg text-green-800 dark:text-green-200">
                  Tambah Artikel Baru
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Judul Artikel</Label>
                    <Input
                      id="title"
                      value={editForm.title}
                      onChange={(e) => setEditForm(prev => ({ ...prev, title: e.target.value }))}
                      placeholder="Masukkan judul artikel"
                    />
                  </div>
                  <div>
                    <Label htmlFor="category">Kategori</Label>
                    <select
                      id="category"
                      value={editForm.category}
                      onChange={(e) => setEditForm(prev => ({ ...prev, category: e.target.value }))}
                      className="w-full h-10 px-3 py-2 text-sm border border-input bg-background rounded-md"
                    >
                      <option value="">Pilih kategori</option>
                      {categories.map(cat => (
                        <option key={cat} value={cat}>{cat}</option>
                      ))}
                    </select>
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="author">Penulis</Label>
                  <Input
                    id="author"
                    value={editForm.author}
                    onChange={(e) => setEditForm(prev => ({ ...prev, author: e.target.value }))}
                    placeholder="Nama penulis"
                  />
                </div>

                <div>
                  <Label htmlFor="excerpt">Ringkasan</Label>
                  <Textarea
                    id="excerpt"
                    value={editForm.excerpt}
                    onChange={(e) => setEditForm(prev => ({ ...prev, excerpt: e.target.value }))}
                    rows={3}
                    placeholder="Ringkasan singkat artikel"
                  />
                </div>

                <div>
                  <Label htmlFor="content">Konten Artikel</Label>
                  <Textarea
                    id="content"
                    value={editForm.content}
                    onChange={(e) => setEditForm(prev => ({ ...prev, content: e.target.value }))}
                    rows={6}
                    placeholder="Tulis konten artikel lengkap di sini"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="featured"
                    checked={editForm.featured}
                    onChange={(e) => setEditForm(prev => ({ ...prev, featured: e.target.checked }))}
                    className="rounded border-gray-300"
                  />
                  <Label htmlFor="featured">Artikel Unggulan</Label>
                </div>

                <div className="flex gap-2">
                  <Button onClick={handleAddPost}>
                    <Save className="w-4 h-4 mr-1" />
                    Simpan Artikel
                  </Button>
                  <Button variant="outline" onClick={handleCancel}>
                    <X className="w-4 h-4 mr-1" />
                    Batal
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Blog Posts List */}
          <div className="space-y-4">
            {blogPosts.map((post) => (
              <Card key={post.id} className="border border-gray-200 dark:border-gray-700">
                <CardContent className="p-6">
                  {editingPost === post.id ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor={`edit-title-${post.id}`}>Judul</Label>
                          <Input
                            id={`edit-title-${post.id}`}
                            value={editForm.title}
                            onChange={(e) => setEditForm(prev => ({ ...prev, title: e.target.value }))}
                          />
                        </div>
                        <div>
                          <Label htmlFor={`edit-category-${post.id}`}>Kategori</Label>
                          <select
                            id={`edit-category-${post.id}`}
                            value={editForm.category}
                            onChange={(e) => setEditForm(prev => ({ ...prev, category: e.target.value }))}
                            className="w-full h-10 px-3 py-2 text-sm border border-input bg-background rounded-md"
                          >
                            {categories.map(cat => (
                              <option key={cat} value={cat}>{cat}</option>
                            ))}
                          </select>
                        </div>
                      </div>
                      
                      <div>
                        <Label htmlFor={`edit-author-${post.id}`}>Penulis</Label>
                        <Input
                          id={`edit-author-${post.id}`}
                          value={editForm.author}
                          onChange={(e) => setEditForm(prev => ({ ...prev, author: e.target.value }))}
                        />
                      </div>

                      <div>
                        <Label htmlFor={`edit-excerpt-${post.id}`}>Ringkasan</Label>
                        <Textarea
                          id={`edit-excerpt-${post.id}`}
                          value={editForm.excerpt}
                          onChange={(e) => setEditForm(prev => ({ ...prev, excerpt: e.target.value }))}
                          rows={3}
                        />
                      </div>

                      <div>
                        <Label htmlFor={`edit-content-${post.id}`}>Konten</Label>
                        <Textarea
                          id={`edit-content-${post.id}`}
                          value={editForm.content}
                          onChange={(e) => setEditForm(prev => ({ ...prev, content: e.target.value }))}
                          rows={6}
                        />
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`edit-featured-${post.id}`}
                          checked={editForm.featured}
                          onChange={(e) => setEditForm(prev => ({ ...prev, featured: e.target.checked }))}
                          className="rounded border-gray-300"
                        />
                        <Label htmlFor={`edit-featured-${post.id}`}>Artikel Unggulan</Label>
                      </div>

                      <div className="flex gap-2">
                        <Button size="sm" onClick={handleSavePost}>
                          <Save className="w-4 h-4 mr-1" />
                          Simpan
                        </Button>
                        <Button size="sm" variant="outline" onClick={handleCancel}>
                          <X className="w-4 h-4 mr-1" />
                          Batal
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                              {post.title}
                            </h3>
                            {post.featured && (
                              <Badge className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                Unggulan
                              </Badge>
                            )}
                          </div>
                          <p className="text-gray-600 dark:text-gray-300 mb-3">
                            {post.excerpt}
                          </p>
                          <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                            <div className="flex items-center gap-1">
                              <User className="w-4 h-4" />
                              {post.author}
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="w-4 h-4" />
                              {new Date(post.date).toLocaleDateString('id-ID')}
                            </div>
                            <Badge variant="outline">{post.category}</Badge>
                          </div>
                        </div>
                        <div className="flex gap-2 ml-4">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleEditPost(post)}
                          >
                            <Pencil className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleDeletePost(post.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
