
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Coffee, Users, FileText, TrendingUp } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export const AdminDashboard = () => {
  const { data: menuCount } = useQuery({
    queryKey: ['admin-menu-count'],
    queryFn: async () => {
      const { count } = await supabase
        .from('menu_items')
        .select('*', { count: 'exact', head: true });
      return count || 0;
    }
  });

  const { data: contentCount } = useQuery({
    queryKey: ['admin-content-count'],
    queryFn: async () => {
      const { count } = await supabase
        .from('site_content')
        .select('*', { count: 'exact', head: true });
      return count || 0;
    }
  });

  const stats = [
    {
      title: 'Total Menu Items',
      value: menuCount || 0,
      icon: Coffee,
      color: 'bg-blue-500'
    },
    {
      title: 'Content Sections',
      value: contentCount || 0,
      icon: FileText,
      color: 'bg-green-500'
    },
    {
      title: 'Active Users',
      value: '-',
      icon: Users,
      color: 'bg-purple-500'
    },
    {
      title: 'Monthly Growth',
      value: '-',
      icon: TrendingUp,
      color: 'bg-orange-500'
    }
  ];

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="border-none shadow-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                    {stat.value}
                  </p>
                </div>
                <div className={`${stat.color} p-3 rounded-full`}>
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card className="border-none shadow-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-red-900 dark:text-red-100">Selamat Datang di Admin Panel</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-red-700 dark:text-red-300">
              Gunakan panel admin ini untuk mengelola seluruh konten website Ider Kopi:
            </p>
            <ul className="list-disc list-inside space-y-2 text-red-600 dark:text-red-400">
              <li><strong>Dashboard:</strong> Lihat statistik dan overview website</li>
              <li><strong>Konten:</strong> Edit teks, gambar, dan informasi di semua halaman</li>
              <li><strong>Menu:</strong> Tambah, edit, atau hapus item menu kopi</li>
            </ul>
            <div className="mt-6 p-4 bg-red-100 dark:bg-red-900 rounded-lg">
              <p className="text-red-800 dark:text-red-200 text-sm">
                <strong>Tips:</strong> Semua perubahan akan langsung tersimpan dan terlihat di website utama.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
