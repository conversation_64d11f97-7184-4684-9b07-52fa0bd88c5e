import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Pencil, Save, X, Plus, Trash2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface MisiItem {
  id: string;
  title: string;
  description: string;
}

export const AdminVisiMisiManager = () => {
  const { toast } = useToast();
  
  // Demo data - in real app this would come from database
  const [visi, setVisi] = useState("Menjadi brand kopi keliling unggulan di Indonesia dengan menghadirkan kopi asli berkualitas tinggi, mendukung petani lokal, menciptakan peluang kerja berkelanjutan, dan memberikan dampak sosial positif bagi masyarakat.");
  
  const [misiItems, setMisiItems] = useState<MisiItem[]>([
    {
      id: '1',
      title: 'Menyediakan Kopi Berkualitas Tinggi',
      description: 'Mengolah dan menyajikan kopi asli dari biji terbaik dengan cita rasa yang konsisten untuk memenuhi kepuasan pelanggan.'
    },
    {
      id: '2',
      title: 'Mendukung Petani Kopi Lokal',
      description: 'Bekerja sama langsung dengan petani kopi lokal untuk memastikan keberlanjutan usaha mereka dan memperkenalkan kopi Nusantara ke pasar yang lebih luas.'
    },
    {
      id: '3',
      title: 'Menciptakan Peluang Kerja yang Berkelanjutan',
      description: 'Membuka lapangan kerja bagi masyarakat dengan membangun sistem kerja yang profesional, mendukung pertumbuhan keterampilan, dan memberikan pendapatan yang layak.'
    }
  ]);

  const [isEditingVisi, setIsEditingVisi] = useState(false);
  const [editVisiValue, setEditVisiValue] = useState('');
  const [editingMisi, setEditingMisi] = useState<string | null>(null);
  const [editMisiTitle, setEditMisiTitle] = useState('');
  const [editMisiDescription, setEditMisiDescription] = useState('');
  const [isAddingMisi, setIsAddingMisi] = useState(false);
  const [newMisiTitle, setNewMisiTitle] = useState('');
  const [newMisiDescription, setNewMisiDescription] = useState('');

  const handleEditVisi = () => {
    setIsEditingVisi(true);
    setEditVisiValue(visi);
  };

  const handleSaveVisi = () => {
    setVisi(editVisiValue);
    setIsEditingVisi(false);
    toast({
      title: "Berhasil!",
      description: "Visi berhasil diperbarui.",
    });
  };

  const handleCancelVisi = () => {
    setIsEditingVisi(false);
    setEditVisiValue('');
  };

  const handleEditMisi = (item: MisiItem) => {
    setEditingMisi(item.id);
    setEditMisiTitle(item.title);
    setEditMisiDescription(item.description);
  };

  const handleSaveMisi = () => {
    if (editingMisi) {
      setMisiItems(prev => prev.map(item => 
        item.id === editingMisi 
          ? { ...item, title: editMisiTitle, description: editMisiDescription }
          : item
      ));
      setEditingMisi(null);
      setEditMisiTitle('');
      setEditMisiDescription('');
      toast({
        title: "Berhasil!",
        description: "Misi berhasil diperbarui.",
      });
    }
  };

  const handleCancelMisi = () => {
    setEditingMisi(null);
    setEditMisiTitle('');
    setEditMisiDescription('');
  };

  const handleAddMisi = () => {
    const newMisi: MisiItem = {
      id: Date.now().toString(),
      title: newMisiTitle,
      description: newMisiDescription
    };
    setMisiItems(prev => [...prev, newMisi]);
    setIsAddingMisi(false);
    setNewMisiTitle('');
    setNewMisiDescription('');
    toast({
      title: "Berhasil!",
      description: "Misi baru berhasil ditambahkan.",
    });
  };

  const handleDeleteMisi = (id: string) => {
    setMisiItems(prev => prev.filter(item => item.id !== id));
    toast({
      title: "Berhasil!",
      description: "Misi berhasil dihapus.",
    });
  };

  return (
    <div className="space-y-6">
      <Card className="border-none shadow-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
        <CardHeader>
          <CardTitle className="text-red-900 dark:text-red-100">Manajemen Visi & Misi</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          
          {/* Visi Section */}
          <Card className="border border-red-200 dark:border-red-800">
            <CardHeader className="pb-4">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg text-red-800 dark:text-red-200">VISI</CardTitle>
                {!isEditingVisi && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleEditVisi}
                  >
                    <Pencil className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {isEditingVisi ? (
                <div className="space-y-3">
                  <Textarea
                    value={editVisiValue}
                    onChange={(e) => setEditVisiValue(e.target.value)}
                    rows={4}
                    className="w-full"
                  />
                  <div className="flex gap-2">
                    <Button size="sm" onClick={handleSaveVisi}>
                      <Save className="w-4 h-4 mr-1" />
                      Simpan
                    </Button>
                    <Button size="sm" variant="outline" onClick={handleCancelVisi}>
                      <X className="w-4 h-4 mr-1" />
                      Batal
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-800 p-4 rounded border">
                  "{visi}"
                </div>
              )}
            </CardContent>
          </Card>

          {/* Misi Section */}
          <Card className="border border-red-200 dark:border-red-800">
            <CardHeader className="pb-4">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg text-red-800 dark:text-red-200">MISI</CardTitle>
                <Button
                  size="sm"
                  onClick={() => setIsAddingMisi(true)}
                  className="bg-red-600 hover:bg-red-700"
                >
                  <Plus className="w-4 h-4 mr-1" />
                  Tambah Misi
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              
              {/* Add New Misi Form */}
              {isAddingMisi && (
                <div className="border border-green-200 dark:border-green-800 rounded-lg p-4 bg-green-50 dark:bg-green-900/10">
                  <div className="space-y-3">
                    <div>
                      <Label>Judul Misi</Label>
                      <Input
                        value={newMisiTitle}
                        onChange={(e) => setNewMisiTitle(e.target.value)}
                        placeholder="Masukkan judul misi"
                      />
                    </div>
                    <div>
                      <Label>Deskripsi</Label>
                      <Textarea
                        value={newMisiDescription}
                        onChange={(e) => setNewMisiDescription(e.target.value)}
                        rows={3}
                        placeholder="Masukkan deskripsi misi"
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button size="sm" onClick={handleAddMisi}>
                        <Save className="w-4 h-4 mr-1" />
                        Tambah
                      </Button>
                      <Button 
                        size="sm" 
                        variant="outline" 
                        onClick={() => {
                          setIsAddingMisi(false);
                          setNewMisiTitle('');
                          setNewMisiDescription('');
                        }}
                      >
                        <X className="w-4 h-4 mr-1" />
                        Batal
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Existing Misi Items */}
              {misiItems.map((item, index) => (
                <div key={item.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div className="text-sm font-medium text-gray-500 dark:text-gray-400">
                      Misi {index + 1}
                    </div>
                    {editingMisi !== item.id && (
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditMisi(item)}
                        >
                          <Pencil className="w-4 h-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDeleteMisi(item.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    )}
                  </div>

                  {editingMisi === item.id ? (
                    <div className="space-y-3">
                      <div>
                        <Label>Judul</Label>
                        <Input
                          value={editMisiTitle}
                          onChange={(e) => setEditMisiTitle(e.target.value)}
                        />
                      </div>
                      <div>
                        <Label>Deskripsi</Label>
                        <Textarea
                          value={editMisiDescription}
                          onChange={(e) => setEditMisiDescription(e.target.value)}
                          rows={3}
                        />
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" onClick={handleSaveMisi}>
                          <Save className="w-4 h-4 mr-1" />
                          Simpan
                        </Button>
                        <Button size="sm" variant="outline" onClick={handleCancelMisi}>
                          <X className="w-4 h-4 mr-1" />
                          Batal
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                        {item.title}
                      </h4>
                      <p className="text-gray-700 dark:text-gray-300">
                        {item.description}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>
        </CardContent>
      </Card>
    </div>
  );
};
