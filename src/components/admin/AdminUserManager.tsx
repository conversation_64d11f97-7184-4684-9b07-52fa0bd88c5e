import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/components/AuthContext';
import { Users, Shield, Mail, Calendar, UserCheck } from 'lucide-react';

interface UserProfile {
  id: string;
  full_name: string | null;
  role: string | null;
  created_at: string | null;
  updated_at: string | null;
}

export const AdminUserManager = () => {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [emailToPromote, setEmailToPromote] = useState('');
  const [promoting, setPromoting] = useState(false);
  const { toast } = useToast();
  const { profile } = useAuth();

  const fetchUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setUsers(data || []);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Gagal memuat data pengguna"
      });
    } finally {
      setLoading(false);
    }
  };

  const promoteToAdmin = async () => {
    if (!emailToPromote.trim()) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Masukkan email yang valid"
      });
      return;
    }

    setPromoting(true);
    try {
      const { error } = await supabase.rpc('set_admin_role', {
        user_email: emailToPromote.trim()
      });

      if (error) throw error;

      toast({
        title: "Berhasil",
        description: `User dengan email ${emailToPromote} telah dipromosikan menjadi admin`
      });
      
      setEmailToPromote('');
      fetchUsers(); // Refresh user list
    } catch (error: any) {
      console.error('Error promoting user:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Gagal mempromosikan user menjadi admin"
      });
    } finally {
      setPromoting(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getRoleBadgeVariant = (role: string | null) => {
    switch (role) {
      case 'admin':
        return 'destructive';
      case 'user':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid gap-6 md:grid-cols-2">
        {/* Promote User Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Promosikan User ke Admin
            </CardTitle>
            <CardDescription>
              Berikan akses admin kepada pengguna berdasarkan email
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Pengguna</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={emailToPromote}
                onChange={(e) => setEmailToPromote(e.target.value)}
              />
            </div>
            <Button 
              onClick={promoteToAdmin}
              disabled={promoting || !emailToPromote.trim()}
              className="w-full"
            >
              {promoting ? 'Memproses...' : 'Promosikan ke Admin'}
            </Button>
          </CardContent>
        </Card>

        {/* Current User Info */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <UserCheck className="w-5 h-5" />
              Informasi Akun Anda
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Nama:</span>
              <span className="text-sm">{profile?.full_name || 'N/A'}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Role:</span>
              <Badge variant={getRoleBadgeVariant(profile?.role)}>
                {profile?.role || 'user'}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Bergabung:</span>
              <span className="text-sm">{formatDate(profile?.created_at)}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Users List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Daftar Pengguna ({users.length})
          </CardTitle>
          <CardDescription>
            Semua pengguna yang terdaftar di sistem
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-500">Memuat data pengguna...</p>
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-8">
              <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Belum ada pengguna terdaftar</p>
            </div>
          ) : (
            <div className="space-y-4">
              {users.map((user) => (
                <div 
                  key={user.id} 
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="font-medium">{user.full_name || 'Nama tidak tersedia'}</h3>
                      <Badge variant={getRoleBadgeVariant(user.role)}>
                        {user.role || 'user'}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Mail className="w-4 h-4" />
                        <span>ID: {user.id.slice(0, 8)}...</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(user.created_at)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
