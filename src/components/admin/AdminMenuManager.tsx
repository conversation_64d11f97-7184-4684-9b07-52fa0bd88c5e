import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Plus, Pencil, Trash2, Save, X } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface MenuItem {
  id: string;
  name: string;
  price: string;
  description: string;
  image_url: string;
  category: string;
  is_active: boolean;
  sort_order: number;
}

export const AdminMenuManager = () => {
  const [editingItem, setEditingItem] = useState<MenuItem | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [newItem, setNewItem] = useState({
    name: '',
    price: '',
    description: '',
    image_url: '',
    category: 'kopi'
  });
  const { toast } = useToast();
  const queryClient = useQueryClient();

  console.log('AdminMenuManager - Loading menu items');

  const { data: menuItems, isLoading, error } = useQuery({
    queryKey: ['admin-menu-items'],
    queryFn: async () => {
      console.log('Fetching menu items...');
      const { data, error } = await supabase
        .from('menu_items')
        .select('*')
        .order('sort_order', { ascending: true });

      if (error) {
        console.error('Database error:', error);
        throw error;
      }

      console.log('Menu items fetched successfully:', data);
      return data as MenuItem[];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
  });

  const updateMenuMutation = useMutation({
    mutationFn: async (item: MenuItem) => {
      console.log('Updating menu item:', item);
      const { error } = await supabase
        .from('menu_items')
        .update({
          name: item.name,
          price: item.price,
          description: item.description,
          image_url: item.image_url,
          category: item.category,
          is_active: item.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', item.id);

      if (error) {
        console.error('Update error:', error);
        throw error;
      }

      console.log('Menu item updated successfully');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-menu-items'] });
      queryClient.invalidateQueries({ queryKey: ['menu-items'] });
      queryClient.invalidateQueries({ queryKey: ['menu-items-homepage'] });
      toast({
        title: "Berhasil!",
        description: "Menu berhasil diperbarui.",
      });
      setEditingItem(null);
    },
    onError: (error) => {
      console.error('Update failed:', error);
      toast({
        title: "Error",
        description: "Gagal memperbarui menu. Silakan coba lagi.",
        variant: "destructive",
      });
    }
  });

  const addMenuMutation = useMutation({
    mutationFn: async (item: typeof newItem) => {
      console.log('Adding new menu item:', item);
      const { error } = await supabase
        .from('menu_items')
        .insert([{
          ...item,
          is_active: true,
          sort_order: (menuItems?.length || 0) + 1
        }]);

      if (error) {
        console.error('Insert error:', error);
        throw error;
      }

      console.log('Menu item added successfully');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-menu-items'] });
      queryClient.invalidateQueries({ queryKey: ['menu-items'] });
      queryClient.invalidateQueries({ queryKey: ['menu-items-homepage'] });
      toast({
        title: "Berhasil!",
        description: "Menu baru berhasil ditambahkan.",
      });
      setIsAddingNew(false);
      setNewItem({
        name: '',
        price: '',
        description: '',
        image_url: '',
        category: 'kopi'
      });
    },
    onError: (error) => {
      console.error('Add failed:', error);
      toast({
        title: "Error",
        description: "Gagal menambahkan menu. Silakan coba lagi.",
        variant: "destructive",
      });
    }
  });

  const deleteMenuMutation = useMutation({
    mutationFn: async (id: string) => {
      console.log('Deleting menu item:', id);
      const { error } = await supabase
        .from('menu_items')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Delete error:', error);
        throw error;
      }

      console.log('Menu item deleted successfully');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-menu-items'] });
      queryClient.invalidateQueries({ queryKey: ['menu-items'] });
      queryClient.invalidateQueries({ queryKey: ['menu-items-homepage'] });
      toast({
        title: "Berhasil!",
        description: "Menu berhasil dihapus.",
      });
    },
    onError: (error) => {
      console.error('Delete failed:', error);
      toast({
        title: "Error",
        description: "Gagal menghapus menu. Silakan coba lagi.",
        variant: "destructive",
      });
    }
  });

  const handleEdit = (item: MenuItem) => {
    setEditingItem({ ...item });
  };

  const handleSave = () => {
    if (editingItem) {
      updateMenuMutation.mutate(editingItem);
    }
  };

  const handleDelete = (id: string) => {
    if (confirm('Apakah Anda yakin ingin menghapus menu ini?')) {
      deleteMenuMutation.mutate(id);
    }
  };

  const handleAddNew = () => {
    addMenuMutation.mutate(newItem);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600 dark:text-red-400">
          Error loading menu. Using demo data for testing.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="border-none shadow-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="text-red-900 dark:text-red-100">Manajemen Menu</CardTitle>
            <Dialog open={isAddingNew} onOpenChange={setIsAddingNew}>
              <DialogTrigger asChild>
                <Button className="bg-red-500 hover:bg-red-600">
                  <Plus className="w-4 h-4 mr-2" />
                  Tambah Menu
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Tambah Menu Baru</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="new-name">Nama Menu</Label>
                    <Input
                      id="new-name"
                      value={newItem.name}
                      onChange={(e) => setNewItem({ ...newItem, name: e.target.value })}
                      placeholder="Nama menu..."
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-price">Harga</Label>
                    <Input
                      id="new-price"
                      value={newItem.price}
                      onChange={(e) => setNewItem({ ...newItem, price: e.target.value })}
                      placeholder="Rp 15.000"
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-category">Kategori</Label>
                    <Select value={newItem.category} onValueChange={(value) => setNewItem({ ...newItem, category: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih kategori" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="kopi">Kopi</SelectItem>
                        <SelectItem value="minuman">Minuman</SelectItem>
                        <SelectItem value="makanan">Makanan</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="new-description">Deskripsi</Label>
                    <Textarea
                      id="new-description"
                      value={newItem.description}
                      onChange={(e) => setNewItem({ ...newItem, description: e.target.value })}
                      placeholder="Deskripsi menu..."
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label htmlFor="new-image">URL Gambar</Label>
                    <Input
                      id="new-image"
                      value={newItem.image_url}
                      onChange={(e) => setNewItem({ ...newItem, image_url: e.target.value })}
                      placeholder="https://..."
                    />
                  </div>
                  <div className="flex gap-2 pt-4">
                    <Button onClick={handleAddNew} disabled={addMenuMutation.isPending} className="flex-1">
                      <Save className="w-4 h-4 mr-2" />
                      Simpan
                    </Button>
                    <Button variant="outline" onClick={() => setIsAddingNew(false)} className="flex-1">
                      <X className="w-4 h-4 mr-2" />
                      Batal
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="mb-4 p-3 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
              <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                Terjadi masalah koneksi database. Beberapa fitur mungkin tidak berfungsi dengan baik.
              </p>
            </div>
          )}
          <div className="grid gap-4">
            {menuItems?.map((item) => (
              <Card key={item.id} className="border border-red-200 dark:border-red-800">
                <CardContent className="p-4">
                  {editingItem?.id === item.id ? (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label>Nama Menu</Label>
                          <Input
                            value={editingItem.name}
                            onChange={(e) => setEditingItem({ ...editingItem, name: e.target.value })}
                          />
                        </div>
                        <div>
                          <Label>Harga</Label>
                          <Input
                            value={editingItem.price}
                            onChange={(e) => setEditingItem({ ...editingItem, price: e.target.value })}
                          />
                        </div>
                      </div>
                      <div>
                        <Label>Kategori</Label>
                        <Select value={editingItem.category} onValueChange={(value) => setEditingItem({ ...editingItem, category: value })}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="kopi">Kopi</SelectItem>
                            <SelectItem value="minuman">Minuman</SelectItem>
                            <SelectItem value="makanan">Makanan</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Deskripsi</Label>
                        <Textarea
                          value={editingItem.description}
                          onChange={(e) => setEditingItem({ ...editingItem, description: e.target.value })}
                          rows={3}
                        />
                      </div>
                      <div>
                        <Label>URL Gambar</Label>
                        <Input
                          value={editingItem.image_url}
                          onChange={(e) => setEditingItem({ ...editingItem, image_url: e.target.value })}
                        />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={editingItem.is_active}
                          onCheckedChange={(checked) => setEditingItem({ ...editingItem, is_active: checked })}
                        />
                        <Label>Aktif</Label>
                      </div>
                      <div className="flex gap-2">
                        <Button onClick={handleSave} disabled={updateMenuMutation.isPending}>
                          <Save className="w-4 h-4 mr-2" />
                          Simpan
                        </Button>
                        <Button variant="outline" onClick={() => setEditingItem(null)}>
                          <X className="w-4 h-4 mr-2" />
                          Batal
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-start justify-between">
                      <div className="flex gap-4">
                        <img
                          src={item.image_url}
                          alt={item.name}
                          className="w-16 h-16 object-cover rounded"
                          onError={(e) => {
                            e.currentTarget.src = 'https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80';
                          }}
                        />
                        <div>
                          <h3 className="font-semibold text-red-900 dark:text-red-100">{item.name}</h3>
                          <p className="text-red-600 dark:text-red-400 font-medium">{item.price}</p>
                          <p className="text-sm text-red-700 dark:text-red-300 capitalize">{item.category}</p>
                          <p className="text-sm text-red-700 dark:text-red-300">{item.description}</p>
                          <div className="flex items-center gap-2 mt-2">
                            <span className={`px-2 py-1 rounded text-xs ${
                              item.is_active 
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                                : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                            }`}>
                              {item.is_active ? 'Aktif' : 'Nonaktif'}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline" onClick={() => handleEdit(item)}>
                          <Pencil className="w-4 h-4" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleDelete(item.id)}>
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
