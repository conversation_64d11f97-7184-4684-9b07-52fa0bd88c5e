import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Save, 
  X, 
  Image as ImageIcon, 
  Eye, 
  EyeOff,
  ArrowUp,
  ArrowDown,
  Monitor
} from 'lucide-react';

interface HeroSlide {
  id: string;
  title: string;
  subtitle: string | null;
  description: string | null;
  image_url: string;
  button_text: string | null;
  button_link: string | null;
  is_active: boolean | null;
  sort_order: number | null;
  created_at: string | null;
  updated_at: string | null;
}

interface SlideFormData {
  title: string;
  subtitle: string;
  description: string;
  image_url: string;
  button_text: string;
  button_link: string;
  is_active: boolean;
}

export const AdminSliderManager = () => {
  const [slides, setSlides] = useState<HeroSlide[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingSlide, setEditingSlide] = useState<HeroSlide | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState<SlideFormData>({
    title: '',
    subtitle: '',
    description: '',
    image_url: '',
    button_text: '',
    button_link: '',
    is_active: true
  });
  const [saving, setSaving] = useState(false);
  const { toast } = useToast();

  const fetchSlides = async () => {
    try {
      const { data, error } = await supabase
        .from('hero_slides')
        .select('*')
        .order('sort_order', { ascending: true });

      if (error) throw error;
      setSlides(data || []);
    } catch (error) {
      console.error('Error fetching slides:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Gagal memuat data slider"
      });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      subtitle: '',
      description: '',
      image_url: '',
      button_text: '',
      button_link: '',
      is_active: true
    });
    setEditingSlide(null);
    setShowAddForm(false);
  };

  const handleEdit = (slide: HeroSlide) => {
    setFormData({
      title: slide.title,
      subtitle: slide.subtitle || '',
      description: slide.description || '',
      image_url: slide.image_url,
      button_text: slide.button_text || '',
      button_link: slide.button_link || '',
      is_active: slide.is_active || false
    });
    setEditingSlide(slide);
    setShowAddForm(false);
  };

  const handleSave = async () => {
    if (!formData.title.trim() || !formData.image_url.trim()) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Title dan Image URL wajib diisi"
      });
      return;
    }

    setSaving(true);
    try {
      const slideData = {
        title: formData.title.trim(),
        subtitle: formData.subtitle.trim() || null,
        description: formData.description.trim() || null,
        image_url: formData.image_url.trim(),
        button_text: formData.button_text.trim() || null,
        button_link: formData.button_link.trim() || null,
        is_active: formData.is_active,
        updated_at: new Date().toISOString()
      };

      if (editingSlide) {
        // Update existing slide
        const { error } = await supabase
          .from('hero_slides')
          .update(slideData)
          .eq('id', editingSlide.id);

        if (error) throw error;

        toast({
          title: "Berhasil",
          description: "Slide berhasil diperbarui"
        });
      } else {
        // Create new slide
        const maxSortOrder = slides.length > 0 ? Math.max(...slides.map(s => s.sort_order || 0)) : 0;
        const { error } = await supabase
          .from('hero_slides')
          .insert({
            ...slideData,
            sort_order: maxSortOrder + 1,
            created_at: new Date().toISOString()
          });

        if (error) throw error;

        toast({
          title: "Berhasil",
          description: "Slide baru berhasil ditambahkan"
        });
      }

      resetForm();
      fetchSlides();
    } catch (error: any) {
      console.error('Error saving slide:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Gagal menyimpan slide"
      });
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (slideId: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus slide ini?')) return;

    try {
      const { error } = await supabase
        .from('hero_slides')
        .delete()
        .eq('id', slideId);

      if (error) throw error;

      toast({
        title: "Berhasil",
        description: "Slide berhasil dihapus"
      });
      fetchSlides();
    } catch (error: any) {
      console.error('Error deleting slide:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Gagal menghapus slide"
      });
    }
  };

  const toggleActive = async (slide: HeroSlide) => {
    try {
      const { error } = await supabase
        .from('hero_slides')
        .update({ 
          is_active: !slide.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', slide.id);

      if (error) throw error;

      toast({
        title: "Berhasil",
        description: `Slide ${!slide.is_active ? 'diaktifkan' : 'dinonaktifkan'}`
      });
      fetchSlides();
    } catch (error: any) {
      console.error('Error toggling slide:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Gagal mengubah status slide"
      });
    }
  };

  const moveSlide = async (slideId: string, direction: 'up' | 'down') => {
    const currentIndex = slides.findIndex(s => s.id === slideId);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= slides.length) return;

    try {
      const slide1 = slides[currentIndex];
      const slide2 = slides[newIndex];

      // Swap sort orders
      await supabase
        .from('hero_slides')
        .update({ sort_order: slide2.sort_order })
        .eq('id', slide1.id);

      await supabase
        .from('hero_slides')
        .update({ sort_order: slide1.sort_order })
        .eq('id', slide2.id);

      toast({
        title: "Berhasil",
        description: "Urutan slide berhasil diubah"
      });
      fetchSlides();
    } catch (error: any) {
      console.error('Error moving slide:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Gagal mengubah urutan slide"
      });
    }
  };

  useEffect(() => {
    fetchSlides();
  }, []);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Kelola Hero Slider
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Tambah, edit, dan kelola slide background halaman depan
          </p>
        </div>
        <Button 
          onClick={() => {
            resetForm();
            setShowAddForm(true);
          }}
          className="bg-blue-500 hover:bg-blue-600"
        >
          <Plus className="w-4 h-4 mr-2" />
          Tambah Slide
        </Button>
      </div>

      {/* Add/Edit Form */}
      {(showAddForm || editingSlide) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ImageIcon className="w-5 h-5" />
              {editingSlide ? 'Edit Slide' : 'Tambah Slide Baru'}
            </CardTitle>
            <CardDescription>
              {editingSlide ? 'Perbarui informasi slide' : 'Buat slide baru untuk hero section'}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  placeholder="Judul utama slide"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="subtitle">Subtitle</Label>
                <Input
                  id="subtitle"
                  placeholder="Subtitle atau badge text"
                  value={formData.subtitle}
                  onChange={(e) => setFormData(prev => ({ ...prev, subtitle: e.target.value }))}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Deskripsi</Label>
              <Textarea
                id="description"
                placeholder="Deskripsi slide"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="image_url">Image URL *</Label>
              <Input
                id="image_url"
                placeholder="https://example.com/image.jpg"
                value={formData.image_url}
                onChange={(e) => setFormData(prev => ({ ...prev, image_url: e.target.value }))}
              />
              {formData.image_url && (
                <div className="mt-2">
                  <img 
                    src={formData.image_url} 
                    alt="Preview" 
                    className="w-full h-32 object-cover rounded-lg"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="button_text">Button Text</Label>
                <Input
                  id="button_text"
                  placeholder="Teks tombol CTA"
                  value={formData.button_text}
                  onChange={(e) => setFormData(prev => ({ ...prev, button_text: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="button_link">Button Link</Label>
                <Input
                  id="button_link"
                  placeholder="/kontak atau https://wa.me/..."
                  value={formData.button_link}
                  onChange={(e) => setFormData(prev => ({ ...prev, button_link: e.target.value }))}
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="is_active"
                checked={formData.is_active}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
              />
              <Label htmlFor="is_active">Aktifkan slide</Label>
            </div>

            <div className="flex gap-2 pt-4">
              <Button 
                onClick={handleSave}
                disabled={saving}
                className="bg-green-500 hover:bg-green-600"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Menyimpan...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Simpan
                  </>
                )}
              </Button>
              <Button 
                variant="outline" 
                onClick={resetForm}
                disabled={saving}
              >
                <X className="w-4 h-4 mr-2" />
                Batal
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Slides List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="w-5 h-5" />
            Daftar Slide ({slides.length})
          </CardTitle>
          <CardDescription>
            Kelola semua slide hero background
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-500">Memuat data slide...</p>
            </div>
          ) : slides.length === 0 ? (
            <div className="text-center py-8">
              <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">Belum ada slide</p>
              <Button 
                onClick={() => setShowAddForm(true)}
                className="bg-blue-500 hover:bg-blue-600"
              >
                <Plus className="w-4 h-4 mr-2" />
                Tambah Slide Pertama
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {slides.map((slide, index) => (
                <div 
                  key={slide.id} 
                  className="flex items-center gap-4 p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  {/* Preview Image */}
                  <div className="w-20 h-12 bg-gray-200 dark:bg-gray-700 rounded overflow-hidden flex-shrink-0">
                    <img 
                      src={slide.image_url} 
                      alt={slide.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.currentTarget.src = '/placeholder.svg';
                      }}
                    />
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium text-gray-900 dark:text-white truncate">
                        {slide.title}
                      </h3>
                      <Badge variant={slide.is_active ? "default" : "secondary"}>
                        {slide.is_active ? "Aktif" : "Nonaktif"}
                      </Badge>
                    </div>
                    {slide.subtitle && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 truncate">
                        {slide.subtitle}
                      </p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">
                      Diperbarui: {formatDate(slide.updated_at)}
                    </p>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-1">
                    {/* Move buttons */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => moveSlide(slide.id, 'up')}
                      disabled={index === 0}
                    >
                      <ArrowUp className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => moveSlide(slide.id, 'down')}
                      disabled={index === slides.length - 1}
                    >
                      <ArrowDown className="w-4 h-4" />
                    </Button>

                    {/* Toggle active */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleActive(slide)}
                    >
                      {slide.is_active ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </Button>

                    {/* Edit */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(slide)}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>

                    {/* Delete */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(slide.id)}
                      className="text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
