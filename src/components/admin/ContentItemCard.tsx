
import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Pencil, Save, X } from 'lucide-react';
import type { ContentItem } from '@/types/admin';

interface ContentItemCardProps {
  item: ContentItem;
  isEditing: boolean;
  editValue: string;
  onEditValueChange: (value: string) => void;
  onEdit: () => void;
  onSave: () => void;
  onCancel: () => void;
  isSaving: boolean;
}

export const ContentItemCard = ({
  item,
  isEditing,
  editValue,
  onEditValueChange,
  onEdit,
  onSave,
  onCancel,
  isSaving
}: ContentItemCardProps) => {
  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
      <div className="flex justify-between items-start mb-2">
        <Label className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
          {item.key.replace('_', ' ')}
        </Label>
        {!isEditing && (
          <Button
            size="sm"
            variant="outline"
            onClick={onEdit}
            className="ml-2"
          >
            <Pencil className="w-4 h-4" />
          </Button>
        )}
      </div>

      {isEditing ? (
        <div className="space-y-3">
          {item.type === 'text' && item.key === 'description' ? (
            <Textarea
              value={editValue}
              onChange={(e) => onEditValueChange(e.target.value)}
              rows={4}
              className="w-full"
            />
          ) : (
            <Input
              value={editValue}
              onChange={(e) => onEditValueChange(e.target.value)}
              className="w-full"
            />
          )}
          <div className="flex gap-2">
            <Button
              size="sm"
              onClick={onSave}
              disabled={isSaving}
            >
              <Save className="w-4 h-4 mr-1" />
              Simpan
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={onCancel}
            >
              <X className="w-4 h-4 mr-1" />
              Batal
            </Button>
          </div>
        </div>
      ) : (
        <div className="text-gray-900 dark:text-gray-100 bg-gray-50 dark:bg-gray-800 p-3 rounded border">
          {item.value || '(Kosong)'}
        </div>
      )}
    </div>
  );
};
