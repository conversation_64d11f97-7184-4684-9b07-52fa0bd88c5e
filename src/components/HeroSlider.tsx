import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel';
import { supabase } from '@/integrations/supabase/client';
import { ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';
import Autoplay from 'embla-carousel-autoplay';

interface HeroSlide {
  id: string;
  title: string;
  subtitle: string | null;
  description: string | null;
  image_url: string;
  button_text: string | null;
  button_link: string | null;
  is_active: boolean | null;
  sort_order: number | null;
}

export const HeroSlider = () => {
  const [slides, setSlides] = useState<HeroSlide[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);

  const autoplayPlugin = React.useRef(
    Autoplay({ delay: 5000, stopOnInteraction: true })
  );

  const fetchSlides = async () => {
    try {
      const { data, error } = await supabase
        .from('hero_slides')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) {
        console.error('Error fetching slides:', error);
        // Fallback to default slide if no slides found
        setSlides([{
          id: 'default',
          title: 'Ider Kopi',
          subtitle: '✨ Kopi Keliling Halal #1 di Ungaran',
          description: 'Kopi premium dengan cita rasa autentik, hadir langsung ke lokasi Anda dengan kualitas terjamin halal',
          image_url: '/lovable-uploads/coffee-bg-1.jpg',
          button_text: '📱 Pesan Sekarang',
          button_link: '/kontak',
          is_active: true,
          sort_order: 1
        }]);
      } else {
        setSlides(data || []);
      }
    } catch (error) {
      console.error('Error fetching slides:', error);
      // Fallback slide
      setSlides([{
        id: 'default',
        title: 'Ider Kopi',
        subtitle: '✨ Kopi Keliling Halal #1 di Ungaran',
        description: 'Kopi premium dengan cita rasa autentik, hadir langsung ke lokasi Anda dengan kualitas terjamin halal',
        image_url: '/lovable-uploads/coffee-bg-1.jpg',
        button_text: '📱 Pesan Sekarang',
        button_link: '/kontak',
        is_active: true,
        sort_order: 1
      }]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSlides();
  }, []);

  const toggleAutoplay = () => {
    if (isPlaying) {
      autoplayPlugin.current.stop();
    } else {
      autoplayPlugin.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  if (loading) {
    return (
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-brand-50 via-white to-brand-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900" />
        <div className="relative z-10 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-brand-500 mx-auto mb-4"></div>
          <p className="text-brand-600 dark:text-brand-400">Memuat slider...</p>
        </div>
      </section>
    );
  }

  if (slides.length === 0) {
    return (
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-brand-50 via-white to-brand-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900" />
        <div className="relative z-10 text-center px-6 max-w-5xl mx-auto pt-20">
          <div className="space-y-8 animate-fade-in">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-brand-100 dark:bg-brand-900/30 border border-brand-200 dark:border-brand-800">
              <span className="text-sm font-medium text-brand-700 dark:text-brand-300">
                ✨ Kopi Keliling Halal #1 di Ungaran
              </span>
            </div>
            <h1 className="text-6xl md:text-8xl font-bold tracking-tight">
              <span className="bg-gradient-to-r from-brand-600 via-brand-700 to-brand-800 bg-clip-text text-transparent">
                Ider Kopi
              </span>
            </h1>
            <p className="text-xl md:text-2xl font-medium text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Belum ada slide yang tersedia. Silakan tambahkan slide melalui admin panel.
            </p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="relative min-h-screen overflow-hidden">
      <Carousel
        plugins={[autoplayPlugin.current]}
        className="w-full h-screen"
        onSelect={(api) => {
          if (api) {
            setCurrentSlide(api.selectedScrollSnap());
          }
        }}
      >
        <CarouselContent>
          {slides.map((slide, index) => (
            <CarouselItem key={slide.id}>
              <div className="relative min-h-screen flex items-center justify-center overflow-hidden">
                {/* Background Image */}
                <div 
                  className="absolute inset-0 bg-cover bg-center bg-no-repeat"
                  style={{
                    backgroundImage: `url(${slide.image_url})`,
                  }}
                />
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-black/40 dark:bg-black/60" />
                
                {/* Content */}
                <div className="relative z-10 text-center px-6 max-w-5xl mx-auto pt-20">
                  <div className="space-y-8 animate-fade-in text-white">
                    {/* Subtitle Badge */}
                    {slide.subtitle && (
                      <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/20 backdrop-blur-sm border border-white/30">
                        <span className="text-sm font-medium text-white">
                          {slide.subtitle}
                        </span>
                      </div>
                    )}

                    {/* Main Title */}
                    <h1 className="text-6xl md:text-8xl font-bold tracking-tight">
                      <span className="bg-gradient-to-r from-white via-gray-100 to-white bg-clip-text text-transparent drop-shadow-lg">
                        {slide.title}
                      </span>
                    </h1>

                    {/* Description */}
                    {slide.description && (
                      <p className="text-xl md:text-2xl font-medium text-white/90 max-w-3xl mx-auto leading-relaxed drop-shadow-md">
                        {slide.description}
                      </p>
                    )}

                    {/* CTA Button */}
                    {slide.button_text && (
                      <div className="pt-6">
                        <Button 
                          size="lg"
                          className="bg-gradient-to-r from-brand-600 to-brand-700 hover:from-brand-700 hover:to-brand-800 text-white px-8 py-4 text-lg font-semibold rounded-xl premium-shadow transition-all duration-300 hover:scale-105"
                          onClick={() => {
                            if (slide.button_link) {
                              if (slide.button_link.startsWith('http')) {
                                window.open(slide.button_link, '_blank');
                              } else {
                                window.location.href = slide.button_link;
                              }
                            }
                          }}
                        >
                          {slide.button_text}
                        </Button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Floating Elements */}
                <div className="absolute inset-0 pointer-events-none overflow-hidden">
                  <div className="absolute top-1/4 left-10 w-2 h-2 bg-white/40 rounded-full opacity-40 animate-bounce" style={{animationDelay: '0s'}} />
                  <div className="absolute top-1/3 right-20 w-3 h-3 bg-white/30 rounded-full opacity-30 animate-bounce" style={{animationDelay: '0.5s'}} />
                  <div className="absolute bottom-1/3 left-1/4 w-4 h-4 bg-white/40 rounded-full opacity-40 animate-bounce" style={{animationDelay: '1s'}} />
                  <div className="absolute bottom-1/4 right-1/3 w-2 h-2 bg-white/30 rounded-full opacity-30 animate-bounce" style={{animationDelay: '1.5s'}} />
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>

        {/* Navigation Arrows */}
        {slides.length > 1 && (
          <>
            <CarouselPrevious className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 backdrop-blur-sm border-white/30 text-white hover:bg-white/30" />
            <CarouselNext className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 backdrop-blur-sm border-white/30 text-white hover:bg-white/30" />
          </>
        )}

        {/* Slide Indicators */}
        {slides.length > 1 && (
          <div className="absolute bottom-8 left-1/2 -translate-x-1/2 flex space-x-2">
            {slides.map((_, index) => (
              <button
                key={index}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide 
                    ? 'bg-white scale-125' 
                    : 'bg-white/50 hover:bg-white/75'
                }`}
                onClick={() => {
                  // Handle slide navigation if needed
                }}
              />
            ))}
          </div>
        )}

        {/* Autoplay Control */}
        {slides.length > 1 && (
          <button
            onClick={toggleAutoplay}
            className="absolute bottom-8 right-8 bg-white/20 backdrop-blur-sm border border-white/30 text-white p-2 rounded-full hover:bg-white/30 transition-all duration-300"
          >
            {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
          </button>
        )}
      </Carousel>
    </section>
  );
};
