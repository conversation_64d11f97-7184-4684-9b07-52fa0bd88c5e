import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

interface UserProfile {
  id: string;
  full_name: string | null;
  role: string;
  created_at: string;
  updated_at: string;
}

interface AuthContextType {
  user: User | null;
  session: Session | null;
  profile: UserProfile | null;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  loading: boolean;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchProfile = async (userId: string) => {
    try {
      console.log('=== Starting fetchProfile for user:', userId);

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Profile fetch timeout')), 10000);
      });

      // Try to fetch the specific profile with timeout
      console.log('=== Attempting to fetch profile for user:', userId);
      const profilePromise = supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .maybeSingle();

      const { data: existingProfile, error: fetchError } = await Promise.race([
        profilePromise,
        timeoutPromise
      ]) as any;

      console.log('=== Profile fetch completed:', { existingProfile, fetchError });

      if (fetchError && fetchError.message !== 'Profile fetch timeout') {
        console.error('=== Error fetching profile:', fetchError);
        // Don't throw error, create fallback profile instead
      }

      if (existingProfile) {
        console.log('=== Profile found, setting profile:', existingProfile);
        setProfile(existingProfile);
        return;
      }

      // If no profile exists or error occurred, create a fallback profile
      console.log('=== No profile found or error occurred, creating fallback profile for user:', userId);
      const fallbackProfile: UserProfile = {
        id: userId,
        full_name: user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'User',
        role: 'user',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      console.log('=== Setting fallback profile:', fallbackProfile);
      setProfile(fallbackProfile);

      // Try to create profile in database in background (don't wait for it)
      supabase
        .from('profiles')
        .insert({
          id: userId,
          full_name: fallbackProfile.full_name,
          role: 'user'
        })
        .then(({ error }) => {
          if (error) {
            console.log('=== Background profile creation failed (this is OK):', error);
          } else {
            console.log('=== Background profile creation succeeded');
          }
        });

    } catch (error) {
      console.error('=== Unexpected error in fetchProfile:', error);
      // Always create a fallback profile to prevent infinite loading
      const fallbackProfile: UserProfile = {
        id: userId,
        full_name: user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'User',
        role: 'user',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      console.log('=== Setting fallback profile due to error:', fallbackProfile);
      setProfile(fallbackProfile);
    } finally {
      console.log('=== fetchProfile completed, setting loading to false');
      setLoading(false);
    }
  };

  useEffect(() => {
    let isMounted = true;
    let loadingTimeout: NodeJS.Timeout;
    console.log('=== AuthProvider useEffect starting');

    // Set a maximum loading time to prevent infinite loading
    loadingTimeout = setTimeout(() => {
      if (isMounted && loading) {
        console.log('=== Loading timeout reached, forcing loading to false');
        setLoading(false);
      }
    }, 15000); // 15 seconds timeout

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('=== Auth state changed:', event, session?.user?.id);

        if (!isMounted) {
          console.log('=== Component unmounted, skipping auth state change');
          return;
        }

        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          console.log('=== User session exists, calling fetchProfile');
          await fetchProfile(session.user.id);
        } else {
          console.log('=== No user session, clearing profile and setting loading false');
          setProfile(null);
          setLoading(false);
        }
      }
    );

    supabase.auth.getSession().then(async ({ data: { session } }) => {
      if (!isMounted) {
        console.log('=== Component unmounted, skipping initial session check');
        return;
      }

      console.log('=== Initial session check:', session?.user?.id);
      setSession(session);
      setUser(session?.user ?? null);

      if (session?.user) {
        console.log('=== Initial session has user, calling fetchProfile');
        await fetchProfile(session.user.id);
      } else {
        console.log('=== No initial session, setting loading to false');
        setLoading(false);
      }
    }).catch((error) => {
      console.error('=== Error getting initial session:', error);
      if (isMounted) {
        setLoading(false);
      }
    });

    return () => {
      console.log('=== AuthProvider cleanup');
      isMounted = false;
      if (loadingTimeout) {
        clearTimeout(loadingTimeout);
      }
      subscription.unsubscribe();
    };
  }, []);

  // Add a useEffect to log profile changes
  useEffect(() => {
    console.log('=== Profile state changed to:', profile);
    console.log('=== Loading state is:', loading);
  }, [profile, loading]);

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { error };
  };

  const signUp = async (email: string, password: string, fullName: string) => {
    const redirectUrl = `${window.location.origin}/`;
    
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectUrl,
        data: {
          full_name: fullName,
        }
      }
    });
    return { error };
  };

  const signOut = async () => {
    try {
      console.log('=== Starting signOut process');
      setLoading(true);

      // Clear local state first
      setProfile(null);
      setUser(null);
      setSession(null);

      // Then sign out from Supabase
      const { error } = await supabase.auth.signOut();

      if (error) {
        console.error('=== SignOut error:', error);
        // Even if there's an error, we've cleared local state
      } else {
        console.log('=== SignOut successful');
      }

      // Force reload to clear any cached state
      window.location.href = '/';

    } catch (error) {
      console.error('=== Unexpected error during signOut:', error);
      // Force clear state and redirect even on error
      setProfile(null);
      setUser(null);
      setSession(null);
      window.location.href = '/';
    } finally {
      setLoading(false);
    }
  };

  const isAdmin = profile?.role === 'admin';

  const value = {
    user,
    session,
    profile,
    signIn,
    signUp,
    signOut,
    loading,
    isAdmin,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
