import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

interface UserProfile {
  id: string;
  full_name: string | null;
  role: string;
  created_at: string;
  updated_at: string;
}

interface AuthContextType {
  user: User | null;
  session: Session | null;
  profile: UserProfile | null;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  loading: boolean;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchProfile = async (userId: string) => {
    try {
      console.log('=== Starting fetchProfile for user:', userId);
      
      // Test basic Supabase connection first
      console.log('=== Testing Supabase connection...');
      const { data: connectionTest, error: connectionError } = await supabase
        .from('profiles')
        .select('count')
        .limit(1);
      
      console.log('=== Connection test result:', { connectionTest, connectionError });
      
      if (connectionError) {
        console.error('=== Supabase connection failed:', connectionError);
        throw connectionError;
      }

      // Now try to fetch the specific profile
      console.log('=== Attempting to fetch profile for user:', userId);
      const { data: existingProfile, error: fetchError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .maybeSingle();

      console.log('=== Profile fetch completed:', { existingProfile, fetchError });

      if (fetchError) {
        console.error('=== Error fetching profile:', fetchError);
        throw fetchError;
      }

      if (existingProfile) {
        console.log('=== Profile found, setting profile:', existingProfile);
        setProfile(existingProfile);
        console.log('=== Profile state should now be set');
        return;
      }

      // If no profile exists, create one
      console.log('=== No profile found, creating new profile for user:', userId);
      const defaultProfile = {
        id: userId,
        full_name: user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'User',
        role: 'user'
      };
      
      console.log('=== Attempting to create profile with data:', defaultProfile);
      
      const { data: newProfile, error: createError } = await supabase
        .from('profiles')
        .insert(defaultProfile)
        .select()
        .single();

      console.log('=== Profile creation result:', { newProfile, createError });

      if (createError) {
        console.error('=== Error creating profile:', createError);
        throw createError;
      }

      console.log('=== Profile created successfully, setting profile:', newProfile);
      setProfile(newProfile);
      
    } catch (error) {
      console.error('=== Unexpected error in fetchProfile:', error);
      // Create a fallback profile to prevent infinite loading
      const fallbackProfile: UserProfile = {
        id: userId,
        full_name: user?.email?.split('@')[0] || 'User',
        role: 'user',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      console.log('=== Setting fallback profile due to error:', fallbackProfile);
      setProfile(fallbackProfile);
    } finally {
      console.log('=== fetchProfile completed, setting loading to false');
      setLoading(false);
    }
  };

  useEffect(() => {
    let isMounted = true;
    console.log('=== AuthProvider useEffect starting');

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('=== Auth state changed:', event, session?.user?.id);
        
        if (!isMounted) {
          console.log('=== Component unmounted, skipping auth state change');
          return;
        }
        
        setSession(session);
        setUser(session?.user ?? null);
        
        if (session?.user) {
          console.log('=== User session exists, calling fetchProfile');
          await fetchProfile(session.user.id);
        } else {
          console.log('=== No user session, clearing profile and setting loading false');
          setProfile(null);
          setLoading(false);
        }
      }
    );

    supabase.auth.getSession().then(async ({ data: { session } }) => {
      if (!isMounted) {
        console.log('=== Component unmounted, skipping initial session check');
        return;
      }
      
      console.log('=== Initial session check:', session?.user?.id);
      setSession(session);
      setUser(session?.user ?? null);
      
      if (session?.user) {
        console.log('=== Initial session has user, calling fetchProfile');
        await fetchProfile(session.user.id);
      } else {
        console.log('=== No initial session, setting loading to false');
        setLoading(false);
      }
    });

    return () => {
      console.log('=== AuthProvider cleanup');
      isMounted = false;
      subscription.unsubscribe();
    };
  }, []);

  // Add a useEffect to log profile changes
  useEffect(() => {
    console.log('=== Profile state changed to:', profile);
    console.log('=== Loading state is:', loading);
  }, [profile, loading]);

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { error };
  };

  const signUp = async (email: string, password: string, fullName: string) => {
    const redirectUrl = `${window.location.origin}/`;
    
    const { error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectUrl,
        data: {
          full_name: fullName,
        }
      }
    });
    return { error };
  };

  const signOut = async () => {
    await supabase.auth.signOut();
    setProfile(null);
  };

  const isAdmin = profile?.role === 'admin';

  const value = {
    user,
    session,
    profile,
    signIn,
    signUp,
    signOut,
    loading,
    isAdmin,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
