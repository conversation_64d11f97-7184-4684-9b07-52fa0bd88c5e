
import React from 'react';

export const Footer = () => {
  return (
    <footer className="bg-gradient-to-br from-red-900 to-rose-900 dark:from-red-950 dark:to-rose-950 text-white py-16">
      <div className="max-w-6xl mx-auto px-4">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="md:col-span-2">
            <div className="flex items-center space-x-3 mb-4">
              <img 
                src="/lovable-uploads/57c53499-1349-4d1a-8d1f-d248355f12d8.png" 
                alt="Ider Kopi Logo" 
                className="w-10 h-10 object-contain"
              />
              <h3 className="text-3xl font-bold text-red-300 dark:text-red-200">Ider <PERSON></h3>
            </div>
            <p className="text-red-100 dark:text-red-50 mb-4 leading-relaxed">
              <PERSON><PERSON> keliling halal No. 1 di Ungaran. Menghadirkan cita rasa premium 
              dengan harga terjangkau langsung ke lokasi Anda.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="w-10 h-10 bg-red-500 dark:bg-red-600 rounded-full flex items-center justify-center text-white hover:bg-red-400 dark:hover:bg-red-500 transition-colors">
                📷
              </a>
              <a href="#" className="w-10 h-10 bg-blue-600 dark:bg-blue-700 rounded-full flex items-center justify-center text-white hover:bg-blue-500 dark:hover:bg-blue-600 transition-colors">
                📘
              </a>
              <a href="#" className="w-10 h-10 bg-green-600 dark:bg-green-700 rounded-full flex items-center justify-center text-white hover:bg-green-500 dark:hover:bg-green-600 transition-colors">
                📱
              </a>
            </div>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-xl font-semibold text-red-300 dark:text-red-200 mb-4">Kontak</h4>
            <div className="space-y-3 text-red-100 dark:text-red-50">
              <div className="flex items-center space-x-2">
                <span>📱</span>
                <span>081-533-1000-01</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>📷</span>
                <span>@iderkopi.id</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>📍</span>
                <span>Ungaran, Kab. Semarang</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-xl font-semibold text-red-300 dark:text-red-200 mb-4">Menu</h4>
            <div className="space-y-2 text-red-100 dark:text-red-50">
              <div>Kopi Susu Ori</div>
              <div>Vanilla Latte</div>
              <div>Salted Caramel</div>
              <div>Choco Hazelnut</div>
            </div>
          </div>
        </div>

        <div className="border-t border-red-700 dark:border-red-800 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-red-200 dark:text-red-100 text-sm mb-4 md:mb-0">
              © 2024 Ider Kopi. All rights reserved. | Halal Certified (ID33310022698060625)
            </div>
            <div className="flex space-x-6 text-sm text-red-200 dark:text-red-100">
              <a href="#" className="hover:text-red-300 dark:hover:text-red-200 transition-colors">Tentang Kami</a>
              <a href="#" className="hover:text-red-300 dark:hover:text-red-200 transition-colors">Menu</a>
              <a href="#" className="hover:text-red-300 dark:hover:text-red-200 transition-colors">Kontak</a>
              <a href="#" className="hover:text-red-300 dark:hover:text-red-200 transition-colors">Syarat & Ketentuan</a>
            </div>
          </div>
        </div>

        {/* Certification Badge */}
        <div className="mt-8 text-center">
          <div className="inline-flex items-center space-x-2 bg-green-600 dark:bg-green-700 px-4 py-2 rounded-full">
            <span className="text-xl">✅</span>
            <span className="text-sm font-semibold">Bersertifikat Halal MUI</span>
          </div>
        </div>
      </div>
    </footer>
  );
};
