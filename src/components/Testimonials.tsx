
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

export const Testimonials = () => {
  const testimonials = [
    {
      text: "<PERSON><PERSON> enak gak harus mahal. Ider <PERSON> buktiin kalau rasa premium bisa terjangkau!",
      author: "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>",
      rating: 5,
      location: "Ungaran"
    },
    {
      text: "Ada Ider Kopi di kantin, pagi jadi semangat! Rasanya konsisten dan pelayanannya ramah.",
      author: "<PERSON><PERSON>, Guru SMANSA",
      rating: 5,
      location: "SMANSA Ungaran"
    },
    {
      text: "Formulasi tanpa SKM ini yang bikin beda. <PERSON><PERSON> kopinya lebih jujur dan tidak eneg.",
      author: "<PERSON><PERSON>, Pegawai Kantor",
      rating: 5,
      location: "Ambarawa"
    },
    {
      text: "Praktis banget! Tinggal pesan, langsung diantar. Kualitas kopi tetap terjaga meski keliling.",
      author: "<PERSON>, Wiraswasta",
      rating: 5,
      location: "Tembalang"
    }
  ];

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-rose-50 to-red-50">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold text-red-900 mb-4">
            Para Pecinta Ider Kopi Bilang...
          </h2>
          <div className="w-24 h-1 bg-red-500 mx-auto mb-6"></div>
          <p className="text-xl text-red-700">
            Testimoni nyata dari pelanggan setia Ider Kopi
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-none bg-white/90 backdrop-blur-sm">
              <CardContent className="p-8">
                <div className="mb-4">
                  <div className="flex text-red-500 mb-3">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <span key={i} className="text-2xl">⭐</span>
                    ))}
                  </div>
                  <blockquote className="text-lg text-red-800 italic leading-relaxed mb-4">
                    "{testimonial.text}"
                  </blockquote>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-semibold text-red-900 text-lg">
                      {testimonial.author}
                    </div>
                    <div className="text-red-600 text-sm">
                      📍 {testimonial.location}
                    </div>
                  </div>
                  <div className="w-12 h-12 bg-gradient-to-br from-red-400 to-rose-500 rounded-full flex items-center justify-center">
                    <span className="text-xl">☕</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center mt-12">
          <div className="bg-gradient-to-r from-red-500 to-rose-500 rounded-2xl p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              Bergabunglah dengan Ribuan Pelanggan Puas!
            </h3>
            <p className="text-white/90 mb-6">
              Rasakan sendiri mengapa Ider Kopi menjadi pilihan utama pecinta kopi di Ungaran
            </p>
            <div className="flex justify-center space-x-8 text-white">
              <div className="text-center">
                <div className="text-3xl font-bold">1000+</div>
                <div className="text-sm">Pelanggan Setia</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">50+</div>
                <div className="text-sm">Lokasi Terjangkau</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">5★</div>
                <div className="text-sm">Rating Rata-rata</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
