import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { useCart } from './Cart';
import { MessageCircle, User, Phone, MapPin, Clock } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface CheckoutProps {
  isOpen: boolean;
  onClose: () => void;
}

interface CustomerInfo {
  name: string;
  phone: string;
  address: string;
  notes: string;
}

const Checkout: React.FC<CheckoutProps> = ({ isOpen, onClose }) => {
  const { items, getTotalPrice, clearCart } = useCart();
  const { toast } = useToast();
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: '',
    phone: '',
    address: '',
    notes: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const generateOrderMessage = () => {
    const orderDate = new Date().toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    let message = `🛒 *PESANAN BARU - IDER KOPI*\n\n`;
    message += `📅 *Tanggal:* ${orderDate}\n\n`;
    
    message += `👤 *Data Pelanggan:*\n`;
    message += `• Nama: ${customerInfo.name}\n`;
    message += `• No. HP: ${customerInfo.phone}\n`;
    message += `• Alamat: ${customerInfo.address}\n\n`;
    
    message += `☕ *Detail Pesanan:*\n`;
    items.forEach((item, index) => {
      const itemPrice = parseInt(item.price.replace(/[^\d]/g, ''));
      const subtotal = itemPrice * item.quantity;
      message += `${index + 1}. ${item.name}\n`;
      message += `   ${item.quantity}x ${item.price} = ${formatPrice(subtotal)}\n`;
    });
    
    message += `\n💰 *Total: ${formatPrice(getTotalPrice())}*\n\n`;
    
    if (customerInfo.notes) {
      message += `📝 *Catatan:* ${customerInfo.notes}\n\n`;
    }
    
    message += `🚚 *Status:* Menunggu Konfirmasi\n\n`;
    message += `Terima kasih telah memesan di Ider Kopi! 🙏`;
    
    return message;
  };

  const sendWhatsAppMessage = async () => {
    try {
      const message = generateOrderMessage();
      const phoneNumber = '6281234567890'; // Ganti dengan nomor WhatsApp bisnis Anda
      
      // Encode message for URL
      const encodedMessage = encodeURIComponent(message);
      
      // Create WhatsApp URL
      const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodedMessage}`;
      
      // Open WhatsApp
      window.open(whatsappUrl, '_blank');
      
      return true;
    } catch (error) {
      console.error('Error sending WhatsApp message:', error);
      return false;
    }
  };

  const sendViaWAHA = async () => {
    try {
      // WAHA API configuration
      const WAHA_BASE_URL = process.env.REACT_APP_WAHA_BASE_URL || 'http://localhost:3000';
      const WAHA_SESSION = process.env.REACT_APP_WAHA_SESSION || 'default';
      const BUSINESS_PHONE = process.env.REACT_APP_BUSINESS_PHONE || '6281234567890';
      
      const message = generateOrderMessage();
      
      const response = await fetch(`${WAHA_BASE_URL}/api/sendText`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session: WAHA_SESSION,
          chatId: `${BUSINESS_PHONE}@c.us`,
          text: message
        })
      });

      if (!response.ok) {
        throw new Error(`WAHA API error: ${response.status}`);
      }

      const result = await response.json();
      console.log('WAHA response:', result);
      
      return true;
    } catch (error) {
      console.error('Error sending via WAHA:', error);
      // Fallback to regular WhatsApp web
      return sendWhatsAppMessage();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!customerInfo.name || !customerInfo.phone || !customerInfo.address) {
      toast({
        variant: "destructive",
        title: "Data Tidak Lengkap",
        description: "Mohon lengkapi semua data yang diperlukan"
      });
      return;
    }

    if (items.length === 0) {
      toast({
        variant: "destructive",
        title: "Keranjang Kosong",
        description: "Tambahkan item ke keranjang terlebih dahulu"
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      const success = await sendViaWAHA();
      
      if (success) {
        toast({
          title: "Pesanan Berhasil Dikirim!",
          description: "Pesanan Anda telah dikirim via WhatsApp. Kami akan segera menghubungi Anda."
        });
        
        // Clear cart and form
        clearCart();
        setCustomerInfo({
          name: '',
          phone: '',
          address: '',
          notes: ''
        });
        onClose();
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Gagal Mengirim Pesanan",
        description: "Terjadi kesalahan saat mengirim pesanan. Silakan coba lagi."
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageCircle className="w-5 h-5 text-green-600" />
            Checkout - Pesan via WhatsApp
          </DialogTitle>
          <DialogDescription>
            Lengkapi data di bawah untuk mengirim pesanan via WhatsApp
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <User className="w-4 h-4" />
                Data Pelanggan
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Nama Lengkap *</Label>
                  <Input
                    id="name"
                    value={customerInfo.name}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Masukkan nama lengkap"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="phone">No. WhatsApp *</Label>
                  <Input
                    id="phone"
                    type="tel"
                    value={customerInfo.phone}
                    onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="08xxxxxxxxxx"
                    required
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="address">Alamat Lengkap *</Label>
                <Textarea
                  id="address"
                  value={customerInfo.address}
                  onChange={(e) => setCustomerInfo(prev => ({ ...prev, address: e.target.value }))}
                  placeholder="Masukkan alamat lengkap untuk pengiriman"
                  required
                />
              </div>
              <div>
                <Label htmlFor="notes">Catatan Tambahan</Label>
                <Textarea
                  id="notes"
                  value={customerInfo.notes}
                  onChange={(e) => setCustomerInfo(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Catatan khusus untuk pesanan (opsional)"
                />
              </div>
            </CardContent>
          </Card>

          {/* Order Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Ringkasan Pesanan</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {items.map((item) => (
                  <div key={item.id} className="flex justify-between items-center">
                    <span>{item.name} x{item.quantity}</span>
                    <span className="font-semibold">
                      {formatPrice(parseInt(item.price.replace(/[^\d]/g, '')) * item.quantity)}
                    </span>
                  </div>
                ))}
                <div className="border-t pt-2 mt-2">
                  <div className="flex justify-between items-center font-bold text-lg">
                    <span>Total:</span>
                    <span className="text-red-600">{formatPrice(getTotalPrice())}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              Batal
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 bg-green-600 hover:bg-green-700"
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              {isSubmitting ? 'Mengirim...' : 'Pesan via WhatsApp'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default Checkout;
