
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Loader2 } from 'lucide-react';
import { Link } from 'react-router-dom';

interface MenuItem {
  id: string;
  name: string;
  price: string;
  description: string;
  image_url: string;
  category: string;
  is_active: boolean;
  sort_order: number;
}

export const Menu = () => {
  // Fetch menu items from database (limit to 4 for homepage)
  const { data: menuItems, isLoading, error } = useQuery({
    queryKey: ['menu-items-homepage'],
    queryFn: async () => {
      console.log('Fetching homepage menu items...');
      const { data, error } = await supabase
        .from('menu_items')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true })
        .limit(4);

      if (error) {
        console.error('Error fetching menu items:', error);
        // Return fallback data if database fails
        return [
          {
            id: '1',
            name: "<PERSON><PERSON>su Ori",
            price: "Rp 15.000",
            description: "Perpaduan sempurna kopi robusta-arabica dengan susu creamy",
            image_url: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80",
            category: "kopi",
            is_active: true,
            sort_order: 1
          },
          {
            id: '2',
            name: "Vanilla Latte",
            price: "Rp 15.000",
            description: "Kopi premium dengan sentuhan vanilla yang memikat",
            image_url: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80",
            category: "kopi",
            is_active: true,
            sort_order: 2
          },
          {
            id: '3',
            name: "Salted Caramel",
            price: "Rp 15.000",
            description: "Manis karamel dengan sentuhan garam yang unik",
            image_url: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80",
            category: "kopi",
            is_active: true,
            sort_order: 3
          },
          {
            id: '4',
            name: "Choco Hazelnut",
            price: "Rp 15.000",
            description: "Cokelat kaya dengan aroma hazelnut yang menggoda",
            image_url: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80",
            category: "kopi",
            is_active: true,
            sort_order: 4
          }
        ];
      }

      return data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  if (isLoading) {
    return (
      <section className="py-20 px-4 bg-gradient-to-br from-red-100 to-rose-100 dark:from-gray-800 dark:to-gray-700">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-red-900 dark:text-red-100 mb-4">
              Menu Favorit Ider Kopi
            </h2>
            <div className="w-24 h-1 bg-red-500 mx-auto mb-6"></div>
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2 text-red-500" />
                <p className="text-red-600 dark:text-red-400 text-sm">Memuat menu...</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-red-100 to-rose-100 dark:from-gray-800 dark:to-gray-700">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold text-red-900 dark:text-red-100 mb-4">
            Menu Favorit Ider Kopi
          </h2>
          <div className="w-24 h-1 bg-red-500 mx-auto mb-6"></div>
          <p className="text-xl text-red-700 dark:text-red-300 max-w-2xl mx-auto">
            Paduan rasa kopi khas Ider: creamy, manis pas, bikin nagih
          </p>
          {error && (
            <div className="mt-4 p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg max-w-md mx-auto">
              <p className="text-yellow-800 dark:text-yellow-200 text-xs">
                Menampilkan menu default
              </p>
            </div>
          )}
        </div>

        {menuItems && menuItems.length > 0 ? (
          <>
            <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8 mb-12">
              {menuItems.map((item) => (
                <Card key={item.id} className="group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-none bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm overflow-hidden">
                  <div className="relative">
                    <img
                      src={item.image_url}
                      alt={item.name}
                      className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80";
                      }}
                    />
                    <div className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full font-semibold text-sm">
                      {item.price}
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                  <CardContent className="p-6">
                    <h3 className="text-2xl font-bold text-red-900 dark:text-red-100 mb-2 group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors">
                      {item.name}
                    </h3>
                    <p className="text-red-700 dark:text-red-300 mb-4 leading-relaxed">
                      {item.description}
                    </p>
                    <Button
                      className="w-full bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-400 hover:to-rose-400 text-white font-semibold transition-all duration-300"
                    >
                      Pesan {item.name}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="text-center">
              <Link to="/menu">
                <Button
                  size="lg"
                  className="bg-red-800 hover:bg-red-700 text-white font-semibold px-8 py-3 text-lg transition-all duration-300 transform hover:scale-105"
                >
                  Lihat Menu Lengkap
                </Button>
              </Link>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-red-600 dark:text-red-400">
              Menu sedang dimuat...
            </p>
          </div>
        )}
      </div>
    </section>
  );
};
