
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export const Menu = () => {
  const menuItems = [
    {
      name: "<PERSON><PERSON>",
      price: "Rp 15.000",
      description: "Perpaduan sempurna kopi robusta-arabica dengan susu creamy",
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80"
    },
    {
      name: "Vanilla Latte",
      price: "Rp 15.000", 
      description: "Kopi premium dengan sentuhan vanilla yang memikat",
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80"
    },
    {
      name: "Salted Caramel",
      price: "Rp 15.000",
      description: "Manis karamel dengan sentuhan garam yang unik",
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80"
    },
    {
      name: "Choco Hazelnut", 
      price: "Rp 15.000",
      description: "Cokelat kaya dengan aroma hazelnut yang menggoda",
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80"
    }
  ];

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-red-100 to-rose-100">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold text-red-900 mb-4">
            Menu Favorit Ider Kopi
          </h2>
          <div className="w-24 h-1 bg-red-500 mx-auto mb-6"></div>
          <p className="text-xl text-red-700 max-w-2xl mx-auto">
            Paduan rasa kopi khas Ider: creamy, manis pas, bikin nagih
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8 mb-12">
          {menuItems.map((item, index) => (
            <Card key={index} className="group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-none bg-white/90 backdrop-blur-sm overflow-hidden">
              <div className="relative">
                <img 
                  src={item.image}
                  alt={item.name}
                  className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full font-semibold text-sm">
                  {item.price}
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>
              <CardContent className="p-6">
                <h3 className="text-2xl font-bold text-red-900 mb-2 group-hover:text-red-600 transition-colors">
                  {item.name}
                </h3>
                <p className="text-red-700 mb-4 leading-relaxed">
                  {item.description}
                </p>
                <Button 
                  className="w-full bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-400 hover:to-rose-400 text-white font-semibold transition-all duration-300"
                >
                  Pesan {item.name}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <Button 
            size="lg"
            className="bg-red-800 hover:bg-red-700 text-white font-semibold px-8 py-3 text-lg transition-all duration-300 transform hover:scale-105"
          >
            Lihat Menu Lengkap
          </Button>
        </div>
      </div>
    </section>
  );
};
