
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export const OrderProcess = () => {
  const steps = [
    {
      number: "1",
      title: "Klik Pesan Sekarang",
      description: "Tekan tombol WhatsApp di bawah atau hubungi langsung",
      icon: "📱",
      color: "from-blue-400 to-blue-600"
    },
    {
      number: "2", 
      title: "<PERSON><PERSON>h Menu Favorit",
      description: "Tentukan kopi pilihan dari menu unggulan kami",
      icon: "☕",
      color: "from-red-400 to-rose-600"
    },
    {
      number: "3",
      title: "Barista Kami Antar",
      description: "Tim terbaik kami langsung ke lokasi Anda",
      icon: "🚲",
      color: "from-green-400 to-green-600"
    },
    {
      number: "4",
      title: "Bayar & Nikmati",
      description: "Bayar langsung, nikmati kopi hangat tanpa ongkir",
      icon: "✨",
      color: "from-red-500 to-rose-600"
    }
  ];

  return (
    <section className="py-20 px-4 bg-gradient-to-r from-red-100 to-rose-100">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl md:text-5xl font-bold text-red-900 mb-4">
            Cara Pesan
          </h2>
          <div className="w-24 h-1 bg-red-500 mx-auto mb-6"></div>
          <p className="text-xl text-red-700">
            4 langkah mudah menuju kopi nikmat
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {steps.map((step, index) => (
            <Card key={index} className="group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-3 border-none bg-white/90 backdrop-blur-sm relative overflow-hidden">
              <div className={`absolute inset-0 bg-gradient-to-br ${step.color} opacity-0 group-hover:opacity-10 transition-opacity duration-300`} />
              <CardContent className="p-8 text-center relative z-10">
                <div className={`w-16 h-16 mx-auto rounded-full bg-gradient-to-br ${step.color} flex items-center justify-center text-white text-2xl font-bold mb-4 transform group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                  {step.number}
                </div>
                <div className="text-4xl mb-4 transform group-hover:scale-110 transition-transform duration-300">
                  {step.icon}
                </div>
                <h3 className="text-xl font-bold text-red-900 mb-3 group-hover:text-red-700 transition-colors">
                  {step.title}
                </h3>
                <p className="text-red-700 text-sm leading-relaxed group-hover:text-red-600 transition-colors">
                  {step.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <div className="bg-white/90 backdrop-blur-sm rounded-2xl p-8 max-w-2xl mx-auto shadow-2xl">
            <h3 className="text-3xl font-bold text-red-900 mb-4">
              Siap Memesan?
            </h3>
            <p className="text-lg text-red-700 mb-6">
              Hubungi kami sekarang dan rasakan kopi keliling terbaik di Ungaran!
            </p>
            
            <div className="space-y-4">
              <Button 
                size="lg"
                className="w-full bg-green-600 hover:bg-green-500 text-white font-bold px-8 py-4 text-lg transition-all duration-300 transform hover:scale-105 shadow-xl"
              >
                📱 Pesan via WhatsApp: 081-533-1000-01
              </Button>
              
              <div className="flex justify-center space-x-4">
                <Button 
                  variant="outline"
                  className="border-2 border-blue-500 text-blue-600 hover:bg-blue-50 font-semibold px-6 py-2"
                >
                  📷 Instagram: @iderkopi.id
                </Button>
                <Button 
                  variant="outline" 
                  className="border-2 border-blue-700 text-blue-800 hover:bg-blue-50 font-semibold px-6 py-2"
                >
                  📘 Facebook
                </Button>
              </div>
            </div>

            <div className="mt-6 p-4 bg-red-100 rounded-lg">
              <p className="text-sm text-red-800">
                <strong>💡 Tips:</strong> Untuk pemesanan grup atau event khusus, 
                hubungi kami H-1 untuk layanan terbaik!
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
