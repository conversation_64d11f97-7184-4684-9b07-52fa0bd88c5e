
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

export const About = () => {
  return (
    <section className="py-24 px-6 bg-gradient-to-b from-white to-brand-50/50 dark:from-gray-900 dark:to-gray-800">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16 space-y-4">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-brand-100 dark:bg-brand-900/30 border border-brand-200 dark:border-brand-800 mb-6">
            <span className="text-sm font-medium text-brand-700 dark:text-brand-300">
              🏆 Kopi Keliling Premium
            </span>
          </div>
          <h2 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white leading-tight">
            Cita Rasa Premium,
            <br />
            <span className="bg-gradient-to-r from-brand-600 to-brand-700 bg-clip-text text-transparent">
              Tradisi Lokal
            </span>
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Kami menghadirkan pengalaman kopi terbaik dengan menggabungkan tradisi lokal 
            dan standar premium internasional
          </p>
        </div>
        
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div className="space-y-8">
            <Card className="glass glass-dark border border-white/20 premium-shadow-lg group hover:scale-105 transition-all duration-500">
              <CardContent className="p-8">
                <div className="flex items-start space-x-6">
                  <div className="w-14 h-14 bg-gradient-to-br from-amber-400 to-amber-600 rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-2xl">🏆</span>
                  </div>
                  <div className="space-y-3">
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                      Pertama & Terdepan
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      Pelopor kopi keliling halal bersertifikat di Ungaran dengan 
                      standar kualitas premium yang konsisten
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="glass glass-dark border border-white/20 premium-shadow-lg group hover:scale-105 transition-all duration-500">
              <CardContent className="p-8">
                <div className="flex items-start space-x-6">
                  <div className="w-14 h-14 bg-gradient-to-br from-brand-500 to-brand-700 rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-2xl">☕</span>
                  </div>
                  <div className="space-y-3">
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                      Formula Rahasia 70:30
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      Perpaduan sempurna 70% Robusta dan 30% Arabica tanpa SKM, 
                      menghasilkan rasa autentik yang tak terlupakan
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="glass glass-dark border border-white/20 premium-shadow-lg group hover:scale-105 transition-all duration-500">
              <CardContent className="p-8">
                <div className="flex items-start space-x-6">
                  <div className="w-14 h-14 bg-gradient-to-br from-emerald-500 to-emerald-700 rounded-2xl flex items-center justify-center flex-shrink-0 group-hover:scale-110 transition-transform duration-300">
                    <span className="text-2xl">🚀</span>
                  </div>
                  <div className="space-y-3">
                    <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                      Layanan Mobile Premium
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                      Teknologi mobile yang memungkinkan kami hadir di mana pun Anda berada, 
                      tanpa mengurangi kualitas rasa dan pelayanan
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="relative">
            <div className="relative rounded-3xl overflow-hidden premium-shadow-lg">
              <img 
                src="https://images.unsplash.com/photo-1721322800607-8c38375eef04?auto=format&fit=crop&q=80"
                alt="Premium Coffee Experience"
                className="w-full h-[600px] object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />
              
              {/* Floating Stats */}
              <div className="absolute top-8 left-8">
                <Card className="glass border border-white/30">
                  <CardContent className="p-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">1000+</div>
                      <div className="text-sm text-white/80">Pelanggan Setia</div>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              <div className="absolute top-8 right-8">
                <Card className="glass border border-white/30">
                  <CardContent className="p-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-white">5.0★</div>
                      <div className="text-sm text-white/80">Rating</div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="absolute bottom-8 left-8 right-8">
                <Card className="glass border border-white/30">
                  <CardContent className="p-6">
                    <div className="text-white text-center">
                      <p className="text-lg font-semibold mb-2">✅ Halal Certified</p>
                      <p className="text-sm opacity-90">ID33310022698060625</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
            
            {/* Decorative Elements */}
            <div className="absolute -top-6 -right-6 w-24 h-24 bg-gradient-to-br from-brand-400/20 to-brand-600/20 rounded-full blur-xl"></div>
            <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-gradient-to-br from-amber-400/20 to-amber-600/20 rounded-full blur-xl"></div>
          </div>
        </div>
      </div>
    </section>
  );
};
