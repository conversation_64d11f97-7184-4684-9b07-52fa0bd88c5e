
import React from 'react';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, User, ArrowRight, Coffee, Newspaper } from 'lucide-react';

const Blog = () => {
  const blogPosts = [
    {
      id: 1,
      title: "Perjalanan Mencari Biji <PERSON>rb<PERSON> da<PERSON>",
      excerpt: "Kisah perjalanan kami menemukan petani kopi lokal di dataran tinggi Aceh yang menghasilkan biji kopi berkualitas premium dengan cita rasa yang unik.",
      author: "<PERSON>",
      date: "15 Juni 2025",
      category: "Eksplorasi",
      image: "https://images.unsplash.com/photo-1447933601403-0c6688de566e?w=800&h=400&fit=crop",
      readTime: "5 min"
    },
    {
      id: 2,
      title: "T<PERSON>s <PERSON> Manual Brewing untuk Pemula",
      excerpt: "Panduan lengkap untuk memulai journey manual brewing di rumah. <PERSON><PERSON> dari pemilihan alat, takaran yang tepat, hingga teknik seduhan yang benar.",
      author: "Barista Ider",
      date: "12 Juni 2025",
      category: "Tutorial",
      image: "https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=800&h=400&fit=crop",
      readTime: "7 min"
    },
    {
      id: 3,
      title: "Dampak Positif Kopi Keliling untuk Ekonomi Lokal",
      excerpt: "Bagaimana konsep kopi keliling dapat memberikan dampak positif bagi perekonomian masyarakat lokal dan menciptakan ekosistem yang berkelanjutan.",
      author: "Tim Research",
      date: "10 Juni 2025",
      category: "Sosial",
      image: "https://images.unsplash.com/photo-1559056199-641a0ac8b55e?w=800&h=400&fit=crop",
      readTime: "6 min"
    },
    {
      id: 4,
      title: "Mengenal Berbagai Jenis Kopi Nusantara",
      excerpt: "Eksplorasi kekayaan kopi Indonesia dari Sabang hingga Merauke. Mengenal karakteristik unik setiap daerah penghasil kopi terbaik di Nusantara.",
      author: "Coffee Explorer",
      date: "8 Juni 2025",
      category: "Edukasi",
      image: "https://images.unsplash.com/photo-1514432324607-a09d9b4aefdd?w=800&h=400&fit=crop",
      readTime: "8 min"
    },
    {
      id: 5,
      title: "Kemitraan dengan Petani: Kisah Sukses dari Jawa Barat",
      excerpt: "Cerita inspiratif tentang kemitraan kami dengan petani kopi di Jawa Barat yang berhasil meningkatkan kualitas hidup melalui program pemberdayaan.",
      author: "Partnership Team",
      date: "5 Juni 2025",
      category: "Kemitraan",
      image: "https://images.unsplash.com/photo-1587734195503-904fca47e0d9?w=800&h=400&fit=crop",
      readTime: "6 min"
    },
    {
      id: 6,
      title: "Inovasi Teknologi dalam Industri Kopi Modern",
      excerpt: "Bagaimana teknologi modern membantu meningkatkan kualitas kopi dan efisiensi dalam proses produksi, dari farm to cup.",
      author: "Tech Innovation",
      date: "3 Juni 2025",
      category: "Teknologi",
      image: "https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?w=800&h=400&fit=crop",
      readTime: "5 min"
    }
  ];

  const categories = [
    { name: "Semua", count: blogPosts.length },
    { name: "Eksplorasi", count: 1 },
    { name: "Tutorial", count: 1 },
    { name: "Sosial", count: 1 },
    { name: "Edukasi", count: 1 },
    { name: "Kemitraan", count: 1 },
    { name: "Teknologi", count: 1 }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-brand-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Header />
      
      <main className="pt-24 pb-16">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-brand-100 dark:bg-brand-900/30 border border-brand-200 dark:border-brand-800 mb-8">
              <Newspaper className="w-4 h-4 text-brand-600 dark:text-brand-400 mr-2" />
              <span className="text-sm font-medium text-brand-700 dark:text-brand-300">
                Blog & Berita
              </span>
            </div>
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              Stories & Updates
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Ikuti perjalanan kami, tips kopi, dan update terbaru dari dunia Ider Kopi
            </p>
          </div>

          {/* Categories Filter */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category, index) => (
              <Button
                key={index}
                variant={index === 0 ? "default" : "outline"}
                className={`rounded-full px-6 py-2 transition-all duration-300 ${
                  index === 0 
                    ? "bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 text-white"
                    : "border-brand-200 dark:border-brand-800 text-gray-700 dark:text-gray-300 hover:bg-brand-50 dark:hover:bg-brand-900/10"
                }`}
              >
                {category.name} ({category.count})
              </Button>
            ))}
          </div>

          {/* Featured Post */}
          <div className="mb-16">
            <Card className="glass glass-dark border-brand-200/20 dark:border-brand-800/20 premium-shadow-lg overflow-hidden">
              <div className="md:flex">
                <div className="md:w-1/2">
                  <img 
                    src={blogPosts[0].image} 
                    alt={blogPosts[0].title}
                    className="w-full h-64 md:h-full object-cover"
                  />
                </div>
                <div className="md:w-1/2 p-8">
                  <div className="flex items-center gap-4 mb-4">
                    <span className="px-3 py-1 bg-brand-100 dark:bg-brand-900/30 text-brand-700 dark:text-brand-300 rounded-full text-sm font-medium">
                      Featured
                    </span>
                    <span className="px-3 py-1 bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full text-sm">
                      {blogPosts[0].category}
                    </span>
                  </div>
                  <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    {blogPosts[0].title}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                    {blogPosts[0].excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                      <div className="flex items-center gap-1">
                        <User className="w-4 h-4" />
                        {blogPosts[0].author}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        {blogPosts[0].date}
                      </div>
                      <span>{blogPosts[0].readTime} baca</span>
                    </div>
                    <Button className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 text-white">
                      Baca Selengkapnya
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Blog Posts Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {blogPosts.slice(1).map((post) => (
              <Card key={post.id} className="glass glass-dark border-brand-200/20 dark:border-brand-800/20 premium-shadow group hover:premium-shadow-lg transition-all duration-300 hover:scale-105 overflow-hidden">
                <div className="relative">
                  <img 
                    src={post.image} 
                    alt={post.title}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="px-3 py-1 bg-white/90 dark:bg-gray-900/90 text-gray-700 dark:text-gray-300 rounded-full text-sm font-medium">
                      {post.category}
                    </span>
                  </div>
                </div>
                <CardHeader className="pb-4">
                  <CardTitle className="text-xl font-bold text-gray-900 dark:text-white leading-tight line-clamp-2">
                    {post.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-4">
                    <div className="flex items-center gap-1">
                      <User className="w-4 h-4" />
                      {post.author}
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {post.date}
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      {post.readTime} baca
                    </span>
                    <Button variant="ghost" size="sm" className="text-brand-600 dark:text-brand-400 hover:text-brand-700 dark:hover:text-brand-300 p-0">
                      Baca <ArrowRight className="w-4 h-4 ml-1" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Newsletter Subscription */}
          <div className="text-center">
            <Card className="glass glass-dark border-brand-200/20 dark:border-brand-800/20 premium-shadow-lg max-w-2xl mx-auto">
              <CardContent className="p-12">
                <div className="w-16 h-16 bg-gradient-to-br from-brand-500 to-brand-600 rounded-3xl flex items-center justify-center mx-auto mb-6">
                  <Coffee className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                  Stay Updated
                </h3>
                <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                  Dapatkan update terbaru tentang dunia kopi, tips brewing, dan berita dari Ider Kopi
                </p>
                <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                  <input
                    type="email"
                    placeholder="Masukkan email Anda"
                    className="flex-1 px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                  />
                  <Button className="bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 text-white px-8 py-3 rounded-xl font-semibold transition-all duration-300 hover:scale-105">
                    Subscribe
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default Blog;
