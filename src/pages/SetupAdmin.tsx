import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { Shield, UserPlus } from 'lucide-react';

const SetupAdmin = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  const promoteToAdmin = async () => {
    if (!email.trim()) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Masukkan email yang valid"
      });
      return;
    }

    setLoading(true);
    try {
      // First check if user exists
      const { data: users, error: userError } = await supabase
        .from('profiles')
        .select('id, full_name, role')
        .eq('id', (await supabase.auth.getUser()).data.user?.id);

      if (userError) {
        console.error('Error checking user:', userError);
      }

      console.log('Current user profile:', users);

      // Try to promote user using RPC function
      const { error } = await supabase.rpc('set_admin_role', {
        user_email: email.trim()
      });

      if (error) {
        console.error('RPC Error:', error);
        throw error;
      }

      toast({
        title: "Berhasil",
        description: `User dengan email ${email} telah dipromosikan menjadi admin`
      });
      
      setEmail('');
      
      // Refresh the page to update auth context
      setTimeout(() => {
        window.location.reload();
      }, 2000);

    } catch (error: any) {
      console.error('Error promoting user:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Gagal mempromosikan user menjadi admin"
      });
    } finally {
      setLoading(false);
    }
  };

  const checkCurrentUser = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      console.log('Current user:', user);
      
      if (user) {
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
        
        console.log('Current profile:', profile);
        console.log('Profile error:', error);
        
        toast({
          title: "User Info",
          description: `Email: ${user.email}, Role: ${profile?.role || 'user'}`
        });
      }
    } catch (error) {
      console.error('Error checking user:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-rose-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
            <Shield className="w-6 h-6 text-red-600 dark:text-red-400" />
          </div>
          <CardTitle className="text-2xl font-bold text-red-900 dark:text-red-100">
            Setup Admin
          </CardTitle>
          <CardDescription>
            Promosikan user menjadi admin untuk mengakses panel admin
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="email">Email User</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>
          
          <Button 
            onClick={promoteToAdmin} 
            disabled={loading}
            className="w-full bg-red-600 hover:bg-red-700"
          >
            <UserPlus className="w-4 h-4 mr-2" />
            {loading ? 'Memproses...' : 'Promosikan ke Admin'}
          </Button>

          <Button 
            onClick={checkCurrentUser} 
            variant="outline"
            className="w-full"
          >
            Cek User Saat Ini
          </Button>

          <div className="text-sm text-gray-600 dark:text-gray-400 text-center">
            <p>Pastikan user sudah terdaftar terlebih dahulu sebelum dipromosikan menjadi admin.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SetupAdmin;
