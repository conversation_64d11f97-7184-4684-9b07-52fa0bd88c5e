
import React from 'react';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const Menu = () => {
  const menuItems = [
    {
      name: "Kopi Susu Ori",
      price: "Rp 15.000",
      description: "Perpaduan sempurna kopi robusta-arabica dengan susu creamy",
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80"
    },
    {
      name: "Vanilla Latte",
      price: "Rp 15.000", 
      description: "Kopi premium dengan sentuhan vanilla yang memikat",
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80"
    },
    {
      name: "Salted Caramel",
      price: "Rp 15.000",
      description: "<PERSON>is karamel dengan sentuhan garam yang unik",
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80"
    },
    {
      name: "Choco Hazelnut", 
      price: "Rp 15.000",
      description: "Cokelat kaya dengan aroma hazelnut yang menggoda",
      image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80"
    },
    {
      name: "Caffe Americano",
      price: "Rp 12.000",
      description: "Kopi hitam premium untuk pecinta kopi murni",
      image: "https://images.unsplash.com/photo-1514432324607-a09d9b4aefdd?auto=format&fit=crop&q=80"
    },
    {
      name: "Cappuccino",
      price: "Rp 16.000",
      description: "Espresso dengan foam susu yang sempurna",
      image: "https://images.unsplash.com/photo-1572442388796-11668a67e53d?auto=format&fit=crop&q=80"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-rose-50 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-24 pb-12 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-red-900 dark:text-red-100 mb-4">
            Menu Ider Kopi
          </h1>
          <div className="w-24 h-1 bg-red-500 mx-auto mb-6"></div>
          <p className="text-xl text-red-700 dark:text-red-300 max-w-2xl mx-auto">
            Nikmati berbagai varian kopi premium dengan cita rasa yang tak terlupakan
          </p>
        </div>
      </section>

      {/* Menu Grid */}
      <section className="py-12 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {menuItems.map((item, index) => (
              <Card key={index} className="group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-none bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm overflow-hidden">
                <div className="relative">
                  <img 
                    src={item.image}
                    alt={item.name}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full font-semibold text-sm">
                    {item.price}
                  </div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-2xl font-bold text-red-900 dark:text-red-100 mb-2 group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors">
                    {item.name}
                  </h3>
                  <p className="text-red-700 dark:text-red-300 mb-4 leading-relaxed">
                    {item.description}
                  </p>
                  <Button className="w-full bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-400 hover:to-rose-400 text-white font-semibold transition-all duration-300">
                    Pesan {item.name}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Menu;
