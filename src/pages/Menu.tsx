
import React, { useState } from 'react';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Loader2, Plus, ShoppingCart } from 'lucide-react';
import { CartProvider, CartSheet, useCart } from '@/components/Cart';
import Checkout from '@/components/Checkout';
import { useToast } from '@/hooks/use-toast';

interface MenuItem {
  id: string;
  name: string;
  price: string;
  description: string;
  image_url: string;
  category: string;
  is_active: boolean;
  sort_order: number;
}

const MenuContent = () => {
  const { addItem } = useCart();
  const { toast } = useToast();
  const [isCheckoutOpen, setIsCheckoutOpen] = useState(false);

  const handleAddToCart = (item: MenuItem) => {
    addItem({
      id: item.id,
      name: item.name,
      price: item.price,
      image_url: item.image_url
    });

    toast({
      title: "Ditambahkan ke keranjang!",
      description: `${item.name} berhasil ditambahkan ke keranjang`,
    });
  };

  const handleCheckout = () => {
    setIsCheckoutOpen(true);
  };
  // Fetch menu items from database
  const { data: menuItems, isLoading, error } = useQuery({
    queryKey: ['menu-items'],
    queryFn: async () => {
      console.log('Fetching menu items...');
      const { data, error } = await supabase
        .from('menu_items')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true });

      if (error) {
        console.error('Error fetching menu items:', error);
        // Return fallback data if database fails
        return [
          {
            id: '1',
            name: "Kopi Susu Ori",
            price: "Rp 15.000",
            description: "Perpaduan sempurna kopi robusta-arabica dengan susu creamy",
            image_url: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80",
            category: "kopi",
            is_active: true,
            sort_order: 1
          },
          {
            id: '2',
            name: "Vanilla Latte",
            price: "Rp 15.000",
            description: "Kopi premium dengan sentuhan vanilla yang memikat",
            image_url: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80",
            category: "kopi",
            is_active: true,
            sort_order: 2
          },
          {
            id: '3',
            name: "Salted Caramel",
            price: "Rp 15.000",
            description: "Manis karamel dengan sentuhan garam yang unik",
            image_url: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80",
            category: "kopi",
            is_active: true,
            sort_order: 3
          },
          {
            id: '4',
            name: "Choco Hazelnut",
            price: "Rp 15.000",
            description: "Cokelat kaya dengan aroma hazelnut yang menggoda",
            image_url: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80",
            category: "kopi",
            is_active: true,
            sort_order: 4
          },
          {
            id: '5',
            name: "Caffe Americano",
            price: "Rp 12.000",
            description: "Kopi hitam premium untuk pecinta kopi murni",
            image_url: "https://images.unsplash.com/photo-1514432324607-a09d9b4aefdd?auto=format&fit=crop&q=80",
            category: "kopi",
            is_active: true,
            sort_order: 5
          },
          {
            id: '6',
            name: "Cappuccino",
            price: "Rp 16.000",
            description: "Espresso dengan foam susu yang sempurna",
            image_url: "https://images.unsplash.com/photo-1572442388796-11668a67e53d?auto=format&fit=crop&q=80",
            category: "kopi",
            is_active: true,
            sort_order: 6
          }
        ];
      }

      return data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: true,
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-rose-50 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
        <Header />
        <div className="pt-24 pb-12 px-4">
          <div className="max-w-6xl mx-auto text-center">
            <div className="flex items-center justify-center py-20">
              <div className="text-center">
                <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-red-500" />
                <p className="text-red-600 dark:text-red-400">Memuat menu...</p>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-rose-50 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <Header />

      {/* Floating Cart Button */}
      <div className="fixed top-20 right-4 z-50">
        <CartSheet onCheckout={handleCheckout} />
      </div>

      {/* Hero Section */}
      <section className="pt-24 pb-12 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-red-900 dark:text-red-100 mb-4">
            Menu Ider Kopi
          </h1>
          <div className="w-24 h-1 bg-red-500 mx-auto mb-6"></div>
          <p className="text-xl text-red-700 dark:text-red-300 max-w-2xl mx-auto">
            Nikmati berbagai varian kopi premium dengan cita rasa yang tak terlupakan
          </p>
          {error && (
            <div className="mt-4 p-3 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
              <p className="text-yellow-800 dark:text-yellow-200 text-sm">
                Menampilkan menu default (koneksi database bermasalah)
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Menu Grid */}
      <section className="py-12 px-4">
        <div className="max-w-6xl mx-auto">
          {menuItems && menuItems.length > 0 ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {menuItems.map((item) => (
                <Card key={item.id} className="group hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border-none bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm overflow-hidden">
                  <div className="relative">
                    <img
                      src={item.image_url}
                      alt={item.name}
                      className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9?auto=format&fit=crop&q=80";
                      }}
                    />
                    <div className="absolute top-4 right-4 bg-red-500 text-white px-3 py-1 rounded-full font-semibold text-sm">
                      {item.price}
                    </div>
                  </div>
                  <CardContent className="p-6">
                    <h3 className="text-2xl font-bold text-red-900 dark:text-red-100 mb-2 group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors">
                      {item.name}
                    </h3>
                    <p className="text-red-700 dark:text-red-300 mb-4 leading-relaxed">
                      {item.description}
                    </p>
                    <Button
                      onClick={() => handleAddToCart(item)}
                      className="w-full bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-400 hover:to-rose-400 text-white font-semibold transition-all duration-300"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Tambah ke Keranjang
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-red-600 dark:text-red-400">
                Tidak ada menu yang tersedia saat ini.
              </p>
            </div>
          )}
        </div>
      </section>

      <Footer />

      {/* Checkout Modal */}
      <Checkout
        isOpen={isCheckoutOpen}
        onClose={() => setIsCheckoutOpen(false)}
      />
    </div>
  );
};

const Menu = () => {
  return (
    <CartProvider>
      <MenuContent />
    </CartProvider>
  );
};

export default Menu;
