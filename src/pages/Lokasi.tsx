
import React from 'react';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MapPin, Clock, Phone } from 'lucide-react';

const Lokasi = () => {
  const locations = [
    {
      name: "SMANSA Ungaran",
      address: "Jl. Diponegoro No.1, Ungaran, Kab. Semarang",
      time: "Senin - Jumat: 07:00 - 15:00",
      status: "Aktif",
      icon: "🏫"
    },
    {
      name: "Area Perkantoran Ungaran",
      address: "<PERSON><PERSON><PERSON>, Ungaran Timur",
      time: "Senin - Sabtu: 08:00 - 17:00",
      status: "Aktif",
      icon: "🏢"
    },
    {
      name: "Terminal Ungaran",
      address: "Terminal Ungaran, Kab. Semarang",
      time: "Setiap Hari: 06:00 - 20:00",
      status: "Aktif",
      icon: "🚌"
    },
    {
      name: "Area Kampus Tembalang",
      address: "<PERSON><PERSON><PERSON>, Tembalang, Semarang",
      time: "Senin - Jumat: 07:30 - 16:00",
      status: "Coming Soon",
      icon: "🎓"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-rose-50 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-24 pb-12 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-red-900 dark:text-red-100 mb-4">
            Lokasi Ider Kopi
          </h1>
          <div className="w-24 h-1 bg-red-500 mx-auto mb-6"></div>
          <p className="text-xl text-red-700 dark:text-red-300 max-w-2xl mx-auto">
            Temukan kami di berbagai lokasi strategis di Ungaran dan sekitarnya
          </p>
        </div>
      </section>

      {/* Coverage Map */}
      <section className="py-12 px-4">
        <div className="max-w-4xl mx-auto">
          <Card className="border-none shadow-xl bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <CardContent className="p-8">
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-red-900 dark:text-red-100 mb-4">Area Jangkauan Kami</h2>
                <div className="flex flex-wrap justify-center gap-4">
                  <span className="bg-red-500 text-white px-4 py-2 rounded-full font-medium">Ungaran</span>
                  <span className="bg-rose-500 text-white px-4 py-2 rounded-full font-medium">Ambarawa</span>
                  <span className="bg-red-600 text-white px-4 py-2 rounded-full font-medium">Tembalang</span>
                  <span className="bg-rose-600 text-white px-4 py-2 rounded-full font-medium">Bawen</span>
                </div>
              </div>
              <div className="bg-gradient-to-r from-red-500 to-rose-500 p-6 rounded-lg text-center">
                <div className="text-6xl mb-4">🚲</div>
                <h3 className="text-2xl font-bold text-white mb-2">Kami Datang ke Kamu!</h3>
                <p className="text-red-100">Layanan antar langsung ke lokasi Anda</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Location List */}
      <section className="py-12 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 gap-8">
            {locations.map((location, index) => (
              <Card key={index} className="border-none shadow-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-xl">{location.icon}</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-xl font-semibold text-red-900 dark:text-red-100">{location.name}</h3>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                          location.status === 'Aktif' 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        }`}>
                          {location.status}
                        </span>
                      </div>
                      <div className="space-y-2 text-red-700 dark:text-red-300">
                        <div className="flex items-center space-x-2">
                          <MapPin className="w-4 h-4" />
                          <span className="text-sm">{location.address}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Clock className="w-4 h-4" />
                          <span className="text-sm">{location.time}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-12 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <Card className="border-none shadow-xl bg-gradient-to-r from-red-500 to-rose-500">
            <CardContent className="p-8">
              <h2 className="text-3xl font-bold text-white mb-4">Ingin Kami Datang ke Lokasi Anda?</h2>
              <p className="text-red-100 mb-6 text-lg">
                Hubungi kami untuk mengatur jadwal kunjungan Ider Kopi ke tempat Anda
              </p>
              <Button className="bg-white text-red-600 hover:bg-red-50 font-semibold px-8 py-3 text-lg">
                <Phone className="w-5 h-5 mr-2" />
                Hubungi Sekarang
              </Button>
            </CardContent>
          </Card>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Lokasi;
