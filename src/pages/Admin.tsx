
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { useAuth } from '@/components/AuthContext';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AdminContentManager } from '@/components/admin/AdminContentManager';
import { AdminMenuManager } from '@/components/admin/AdminMenuManager';
import { AdminDashboard } from '@/components/admin/AdminDashboard';
import { AdminVisiMisiManager } from '@/components/admin/AdminVisiMisiManager';
import { AdminBlogManager } from '@/components/admin/AdminBlogManager';
import { AdminUserManager } from '@/components/admin/AdminUserManager';
import { AdminSliderManager } from '@/components/admin/AdminSliderManager';
import { Settings, Coffee, FileText, BarChart3, Target, BookOpen, LogOut, User, Users, Monitor } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

const Admin = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const { profile, signOut } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleLogout = async () => {
    try {
      await signOut();
      toast({
        title: "Berhasil logout",
        description: "Anda telah keluar dari sistem"
      });
      navigate('/');
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error logout",
        description: "Terjadi kesalahan saat logout"
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-rose-50 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <Header />
      
      <div className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="mb-8">
            <div className="flex justify-between items-start">
              <div>
                <h1 className="text-4xl font-bold text-red-900 dark:text-red-100 mb-2">
                  Admin Panel
                </h1>
                <p className="text-red-700 dark:text-red-300">
                  Kelola konten dan pengaturan website Ider Kopi
                </p>
                <div className="flex items-center gap-2 mt-2">
                  <User className="w-4 h-4 text-red-600 dark:text-red-400" />
                  <p className="text-sm text-red-600 dark:text-red-400">
                    Selamat datang, {profile?.full_name || 'Admin'}
                  </p>
                </div>
              </div>
              <Button
                onClick={handleLogout}
                variant="outline"
                className="border-red-300 text-red-600 hover:bg-red-50 dark:border-red-700 dark:text-red-400 dark:hover:bg-red-900/20"
              >
                <LogOut className="w-4 h-4 mr-2" />
                Logout
              </Button>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-7 lg:w-auto lg:grid-cols-7">
              <TabsTrigger value="dashboard" className="flex items-center gap-2">
                <BarChart3 className="w-4 h-4" />
                Dashboard
              </TabsTrigger>
              <TabsTrigger value="slider" className="flex items-center gap-2">
                <Monitor className="w-4 h-4" />
                Slider
              </TabsTrigger>
              <TabsTrigger value="content" className="flex items-center gap-2">
                <FileText className="w-4 h-4" />
                Konten
              </TabsTrigger>
              <TabsTrigger value="menu" className="flex items-center gap-2">
                <Coffee className="w-4 h-4" />
                Menu
              </TabsTrigger>
              <TabsTrigger value="visi-misi" className="flex items-center gap-2">
                <Target className="w-4 h-4" />
                Visi Misi
              </TabsTrigger>
              <TabsTrigger value="blog" className="flex items-center gap-2">
                <BookOpen className="w-4 h-4" />
                Blog
              </TabsTrigger>
              <TabsTrigger value="users" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Users
              </TabsTrigger>
            </TabsList>

            <TabsContent value="dashboard" className="space-y-6">
              <AdminDashboard />
            </TabsContent>

            <TabsContent value="slider" className="space-y-6">
              <AdminSliderManager />
            </TabsContent>

            <TabsContent value="content" className="space-y-6">
              <AdminContentManager />
            </TabsContent>

            <TabsContent value="menu" className="space-y-6">
              <AdminMenuManager />
            </TabsContent>

            <TabsContent value="visi-misi" className="space-y-6">
              <AdminVisiMisiManager />
            </TabsContent>

            <TabsContent value="blog" className="space-y-6">
              <AdminBlogManager />
            </TabsContent>

            <TabsContent value="users" className="space-y-6">
              <AdminUserManager />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Admin;
