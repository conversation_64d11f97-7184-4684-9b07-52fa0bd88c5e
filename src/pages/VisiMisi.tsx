
import React from 'react';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Target, Heart, Users, Lightbulb, Shield, DollarSign } from 'lucide-react';

const VisiMisi = () => {
  const misiItems = [
    {
      icon: Target,
      title: "Menyediakan Kopi Berkualitas Tinggi",
      description: "Mengolah dan menyajikan kopi asli dari biji terbaik dengan cita rasa yang konsisten untuk memenuhi kepuasan pelanggan."
    },
    {
      icon: Heart,
      title: "Mendukung Petani Kopi Lokal",
      description: "Bekerja sama langsung dengan petani kopi lokal untuk memastikan keberlanjutan usaha mereka dan memperkenalkan kopi Nusantara ke pasar yang lebih luas."
    },
    {
      icon: Users,
      title: "Menciptakan Peluang Kerja yang Berkelanjutan",
      description: "Membuka lapangan kerja bagi masyarakat dengan membangun sistem kerja yang profesional, mendukung pertum<PERSON>han keterampilan, dan memberikan pendapatan yang layak."
    },
    {
      icon: Heart,
      title: "Memberikan Dampak Sosial Positif",
      description: "Berkontribusi pada masyarakat dengan mendukung program-program sosial dan pendidikan, serta membangun kesadaran akan pentingnya keberlanjutan dan tanggung jawab sosial."
    },
    {
      icon: Lightbulb,
      title: "Menjadi Solusi Inovatif di Industri Kopi",
      description: "Menghadirkan konsep kopi keliling yang fleksibel, modern, dan ramah lingkungan untuk menjangkau pelanggan di berbagai lokasi."
    },
    {
      icon: DollarSign,
      title: "Menjaga Keterjangkauan Produk",
      description: "Menawarkan harga yang kompetitif agar kopi berkualitas dapat dinikmati oleh semua kalangan, tanpa mengorbankan mutu dan nilai produk."
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-brand-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <Header />
      
      <main className="pt-24 pb-16">
        <div className="max-w-7xl mx-auto px-6 lg:px-8">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-brand-100 dark:bg-brand-900/30 border border-brand-200 dark:border-brand-800 mb-8">
              <span className="text-sm font-medium text-brand-700 dark:text-brand-300">
                ✨ Tentang Kami
              </span>
            </div>
            <h1 className="text-5xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              Visi & Misi
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Komitmen kami untuk memberikan yang terbaik bagi pelanggan, petani, dan masyarakat
            </p>
          </div>

          {/* Visi Section */}
          <div className="mb-20">
            <Card className="glass glass-dark border-brand-200/20 dark:border-brand-800/20 premium-shadow-lg">
              <CardHeader className="text-center pb-8">
                <div className="w-20 h-20 bg-gradient-to-br from-brand-500 to-brand-700 rounded-3xl flex items-center justify-center mx-auto mb-6 premium-shadow">
                  <Target className="w-10 h-10 text-white" />
                </div>
                <CardTitle className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                  VISI
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="bg-gradient-to-r from-brand-50 to-brand-100 dark:from-brand-900/20 dark:to-brand-800/20 rounded-2xl p-8 border border-brand-200/30 dark:border-brand-700/30">
                  <p className="text-lg md:text-xl text-gray-700 dark:text-gray-300 leading-relaxed text-center font-medium">
                    "Menjadi brand kopi keliling unggulan di Indonesia dengan menghadirkan kopi asli berkualitas tinggi, 
                    mendukung petani lokal, menciptakan peluang kerja berkelanjutan, dan memberikan dampak sosial positif bagi masyarakat."
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Misi Section */}
          <div>
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">MISI</h2>
              <p className="text-lg text-gray-600 dark:text-gray-300">
                Langkah-langkah konkret untuk mewujudkan visi kami
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {misiItems.map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <Card key={index} className="glass glass-dark border-brand-200/20 dark:border-brand-800/20 premium-shadow group hover:premium-shadow-lg transition-all duration-300 hover:scale-105">
                    <CardHeader className="pb-4">
                      <div className="w-16 h-16 bg-gradient-to-br from-brand-500 to-brand-600 rounded-2xl flex items-center justify-center mb-4 group-hover:from-brand-600 group-hover:to-brand-700 transition-all duration-300">
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      <CardTitle className="text-xl font-bold text-gray-900 dark:text-white leading-tight">
                        {item.title}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                        {item.description}
                      </p>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-20">
            <Card className="glass glass-dark border-brand-200/20 dark:border-brand-800/20 premium-shadow-lg">
              <CardContent className="p-12">
                <h3 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
                  Bergabunglah dengan Misi Kami
                </h3>
                <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
                  Mari bersama-sama membangun ekosistem kopi yang berkelanjutan dan memberikan dampak positif bagi semua
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button className="bg-gradient-to-r from-brand-600 to-brand-700 hover:from-brand-700 hover:to-brand-800 text-white px-8 py-4 rounded-xl font-semibold transition-all duration-300 hover:scale-105 premium-shadow">
                    Hubungi Kami
                  </button>
                  <button className="border-2 border-brand-300 dark:border-brand-700 text-brand-700 dark:text-brand-300 hover:bg-brand-50 dark:hover:bg-brand-900/10 px-8 py-4 rounded-xl font-semibold transition-all duration-300">
                    Pelajari Lebih Lanjut
                  </button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default VisiMisi;
