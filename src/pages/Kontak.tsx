
import React from 'react';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Phone, MessageCircle, Instagram, MapPin, Clock, Mail } from 'lucide-react';

const Kontak = () => {
  const contactMethods = [
    {
      icon: <Phone className="w-8 h-8" />,
      title: "WhatsApp",
      value: "************-01",
      description: "Chat langsung untuk pemesanan",
      color: "bg-green-500",
      action: "Chat WhatsApp"
    },
    {
      icon: <Instagram className="w-8 h-8" />,
      title: "Instagram",
      value: "@iderkopi.id",
      description: "Follow untuk update terbaru",
      color: "bg-pink-500",
      action: "Follow Instagram"
    },
    {
      icon: <MessageCircle className="w-8 h-8" />,
      title: "Telegram",
      value: "@iderkopi",
      description: "Pesan cepat via Telegram",
      color: "bg-blue-500",
      action: "Chat Telegram"
    },
    {
      icon: <Mail className="w-8 h-8" />,
      title: "Email",
      value: "<EMAIL>",
      description: "Untuk kerjasama bisnis",
      color: "bg-red-500",
      action: "Kirim Email"
    }
  ];

  const businessInfo = [
    {
      icon: <MapPin className="w-6 h-6" />,
      title: "Area Layanan",
      value: "Ungaran, Ambarawa, Tembalang, Bawen"
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: "Jam Operasional",
      value: "Senin - Sabtu: 07:00 - 17:00"
    },
    {
      icon: <Phone className="w-6 h-6" />,
      title: "Response Time",
      value: "< 30 menit (jam kerja)"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-rose-50 dark:from-gray-900 dark:to-gray-800 transition-colors duration-300">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-24 pb-12 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-red-900 dark:text-red-100 mb-4">
            Hubungi Kami
          </h1>
          <div className="w-24 h-1 bg-red-500 mx-auto mb-6"></div>
          <p className="text-xl text-red-700 dark:text-red-300 max-w-2xl mx-auto">
            Siap melayani Anda kapan saja. Hubungi kami untuk pemesanan atau informasi lebih lanjut
          </p>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-12 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {contactMethods.map((method, index) => (
              <Card key={index} className="border-none shadow-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <CardContent className="p-6 text-center">
                  <div className={`${method.color} w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-white`}>
                    {method.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-red-900 dark:text-red-100 mb-2">{method.title}</h3>
                  <p className="text-red-600 dark:text-red-400 font-medium mb-2">{method.value}</p>
                  <p className="text-red-700 dark:text-red-300 text-sm mb-4">{method.description}</p>
                  <Button className={`w-full ${method.color} hover:opacity-90 text-white transition-all duration-300`}>
                    {method.action}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Business Info */}
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {businessInfo.map((info, index) => (
              <Card key={index} className="border-none shadow-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center flex-shrink-0">
                      <div className="text-red-600 dark:text-red-400">
                        {info.icon}
                      </div>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-red-900 dark:text-red-100 mb-1">{info.title}</h3>
                      <p className="text-red-700 dark:text-red-300">{info.value}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Quick Order Section */}
          <Card className="border-none shadow-xl bg-gradient-to-r from-red-500 to-rose-500 mb-16">
            <CardContent className="p-8 text-center">
              <div className="text-6xl mb-4">☕</div>
              <h2 className="text-3xl font-bold text-white mb-4">Pesan Sekarang Juga!</h2>
              <p className="text-red-100 mb-6 text-lg">
                Kopi segar siap diantar langsung ke lokasi Anda dalam 30 menit
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-white text-red-600 hover:bg-red-50 font-semibold px-8 py-3 text-lg">
                  <Phone className="w-5 h-5 mr-2" />
                  Telepon Langsung
                </Button>
                <Button className="bg-green-600 hover:bg-green-500 text-white font-semibold px-8 py-3 text-lg">
                  <MessageCircle className="w-5 h-5 mr-2" />
                  WhatsApp
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* FAQ Section */}
          <Card className="border-none shadow-lg bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <CardContent className="p-8">
              <h2 className="text-2xl font-bold text-red-900 dark:text-red-100 mb-6 text-center">
                Pertanyaan Umum
              </h2>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                    Berapa lama waktu pengantaran?
                  </h3>
                  <p className="text-red-700 dark:text-red-300">
                    Kami akan tiba di lokasi Anda maksimal 30 menit setelah pemesanan dikonfirmasi.
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                    Apakah ada minimum order?
                  </h3>
                  <p className="text-red-700 dark:text-red-300">
                    Minimum order 2 cup untuk area Ungaran, dan 5 cup untuk area luar Ungaran.
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                    Bagaimana cara pembayaran?
                  </h3>
                  <p className="text-red-700 dark:text-red-300">
                    Kami menerima pembayaran tunai, transfer bank, e-wallet (GoPay, OVO, DANA), dan QRIS.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Kontak;
