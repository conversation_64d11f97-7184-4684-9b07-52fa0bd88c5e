
import React from 'react';
import { Head<PERSON> } from '@/components/Header';
import { Hero } from '@/components/Hero';
import { About } from '@/components/About';
import { Menu } from '@/components/Menu';
import { Features } from '@/components/Features';
import { Location } from '@/components/Location';
import { Testimonials } from '@/components/Testimonials';
import { OrderProcess } from '@/components/OrderProcess';
import { Footer } from '@/components/Footer';

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-brand-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 transition-colors duration-300">
      <Header />
      <main className="space-y-0">
        <Hero />
        <About />
        <Menu />
        <Features />
        <Location />
        <Testimonials />
        <OrderProcess />
      </main>
      <Footer />
    </div>
  );
};

export default Index;
