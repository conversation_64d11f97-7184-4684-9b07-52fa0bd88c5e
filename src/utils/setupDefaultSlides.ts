import { supabase } from '@/integrations/supabase/client';

export const setupDefaultSlides = async () => {
  try {
    // Check if slides already exist
    const { data: existingSlides, error: checkError } = await supabase
      .from('hero_slides')
      .select('id')
      .limit(1);

    if (checkError) {
      console.error('Error checking existing slides:', checkError);
      return { success: false, error: checkError.message };
    }

    if (existingSlides && existingSlides.length > 0) {
      return { success: true, message: 'Slides already exist' };
    }

    // Default slides data
    const defaultSlides = [
      {
        title: 'Ider Kopi',
        subtitle: '✨ Kopi Keliling Halal #1 di Ungaran',
        description: 'Kopi premium dengan cita rasa autentik, hadir langsung ke lokasi Anda dengan kualitas terjamin halal',
        image_url: 'https://images.unsplash.com/photo-1447933601403-0c6688de566e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
        button_text: '📱 Pesan Sekarang',
        button_link: '/kontak',
        is_active: true,
        sort_order: 1
      },
      {
        title: 'Kualitas Premium',
        subtitle: '🌟 Biji Kopi Pilihan Terbaik',
        description: 'Menggunakan biji kopi robusta dan arabica dengan perbandingan 70:30 untuk cita rasa yang sempurna',
        image_url: 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
        button_text: '☕ Lihat Menu',
        button_link: '/menu',
        is_active: true,
        sort_order: 2
      },
      {
        title: 'Layanan Terpercaya',
        subtitle: '🚚 Antar Langsung ke Lokasi Anda',
        description: 'Melayani area Ungaran dan sekitarnya dengan komitmen kualitas dan kepuasan pelanggan',
        image_url: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
        button_text: '📍 Cek Lokasi',
        button_link: '/lokasi',
        is_active: true,
        sort_order: 3
      }
    ];

    // Insert default slides
    const { error: insertError } = await supabase
      .from('hero_slides')
      .insert(defaultSlides.map(slide => ({
        ...slide,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })));

    if (insertError) {
      console.error('Error inserting default slides:', insertError);
      return { success: false, error: insertError.message };
    }

    return { 
      success: true, 
      message: `${defaultSlides.length} slide default berhasil ditambahkan` 
    };

  } catch (error: any) {
    console.error('Unexpected error in setupDefaultSlides:', error);
    return { success: false, error: error.message };
  }
};

export const checkSlidesExist = async () => {
  try {
    const { data, error } = await supabase
      .from('hero_slides')
      .select('id')
      .limit(1);

    if (error) {
      console.error('Error checking slides:', error);
      return false;
    }

    return data && data.length > 0;
  } catch (error) {
    console.error('Error checking slides:', error);
    return false;
  }
};
