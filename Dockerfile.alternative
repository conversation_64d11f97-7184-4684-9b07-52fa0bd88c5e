# Alternative Dockerfile with better error handling
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Install build dependencies
RUN apk add --no-cache python3 make g++

# Copy package files first for better caching
COPY package*.json ./

# Clear npm cache and install dependencies
RUN npm cache clean --force && \
    npm install --frozen-lockfile

# Copy source code
COPY . .

# Build the application with error handling
RUN npm run build || (echo "Build failed, checking files:" && ls -la && exit 1)

# Verify build output
RUN ls -la dist/ || (echo "Dist folder not found!" && exit 1)

# Production stage
FROM nginx:alpine

# Install curl for health checks
RUN apk add --no-cache curl

# Remove default nginx content
RUN rm -rf /usr/share/nginx/html/*

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Verify files were copied
RUN ls -la /usr/share/nginx/html/

# Create health check endpoint
RUN echo "healthy" > /usr/share/nginx/html/health

# Create fallback index.html if needed
RUN if [ ! -f /usr/share/nginx/html/index.html ]; then \
    echo '<!DOCTYPE html><html><head><title>Ider Kopi</title></head><body><h1>Loading...</h1></body></html>' > /usr/share/nginx/html/index.html; \
    fi

# Set proper permissions
RUN chmod -R 755 /usr/share/nginx/html

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
