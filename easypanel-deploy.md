# 🚀 EasyPanel Deployment - Simplified Guide

## Option 1: Static Site Deployment (Recommended)

EasyPanel dapat deploy React app sebagai static site tanpa Docker custom.

### Step 1: Configure Build Settings di EasyPanel

```yaml
# Build Configuration
Framework: React/Vite
Build Command: npm run build
Output Directory: dist
Node Version: 18
Install Command: npm ci
```

### Step 2: Environment Variables

Set di EasyPanel Dashboard:
```bash
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
NODE_ENV=production
```

### Step 3: Deploy

1. Push code ke Git repository
2. Connect repository di EasyPanel
3. Set build configuration
4. Set environment variables
5. Deploy

## Option 2: Docker Deployment (Advanced)

Jika ingin menggunakan custom Docker configuration:

### Fixed Dockerfile

```dockerfile
# Dockerfile for EasyPanel
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies (including dev for build)
RUN npm ci

# Copy source
COPY . .

# Build
RUN npm run build

# Production
FROM nginx:alpine

# Install curl
RUN apk add --no-cache curl

# Copy nginx config
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built app
COPY --from=builder /app/dist /usr/share/nginx/html

# Health check file
RUN echo "healthy" > /usr/share/nginx/html/health

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

### Simplified nginx.conf

```nginx
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    sendfile on;
    keepalive_timeout 65;

    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml;

    server {
        listen 80;
        root /usr/share/nginx/html;
        index index.html;

        # React Router support
        location / {
            try_files $uri $uri/ /index.html;
        }

        # Static files caching
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Health check
        location /health {
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
```

## Troubleshooting

### Error: npm ci --only=production
**Solution**: Use `npm ci` instead (fixed in new Dockerfile)

### Error: vite: not found
**Solution**: Install all dependencies including devDependencies for build

### Error: curl not found
**Solution**: Install curl in Alpine: `RUN apk add --no-cache curl`

## Recommended Approach

**Use Option 1 (Static Site)** karena:
- ✅ Lebih sederhana
- ✅ Faster deployment
- ✅ Less configuration
- ✅ EasyPanel optimized
- ✅ Automatic HTTPS
- ✅ Built-in CDN

## Quick Deploy Steps

1. **Push to Git**:
   ```bash
   git add .
   git commit -m "Ready for EasyPanel deployment"
   git push origin main
   ```

2. **EasyPanel Setup**:
   - Create new app
   - Connect Git repository
   - Select "Static Site" or "React/Vite"
   - Set build command: `npm run build`
   - Set output directory: `dist`

3. **Environment Variables**:
   ```
   VITE_SUPABASE_URL=your-url
   VITE_SUPABASE_ANON_KEY=your-key
   ```

4. **Deploy**: Click deploy button

## Verification

After deployment:
- ✅ Website loads
- ✅ Slider works
- ✅ Authentication works
- ✅ Admin panel accessible
- ✅ Database connected

**Ready to deploy! 🚀**
