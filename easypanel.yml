# EasyPanel Configuration for Ider Kopi Website
# This file contains the configuration for deploying to EasyPanel

name: ider-kopi-ungaran
description: Website Ider Kopi Ungaran - <PERSON><PERSON> Keliling Halal #1 di Ungaran

# Application Configuration
app:
  name: ider-kopi-web
  type: web
  framework: react
  
  # Build Configuration
  build:
    command: npm run build
    output: dist
    node_version: "18"
    
  # Runtime Configuration
  runtime:
    port: 80
    health_check: /health
    
  # Environment Variables (set these in EasyPanel dashboard)
  environment:
    - VITE_SUPABASE_URL
    - VITE_SUPABASE_ANON_KEY
    - NODE_ENV=production
    
  # Domain Configuration
  domains:
    - your-domain.com
    - www.your-domain.com
    
  # SSL Configuration
  ssl:
    enabled: true
    force_https: true
    
  # Resource Limits
  resources:
    memory: 512MB
    cpu: 0.5
    
  # Scaling Configuration
  scaling:
    min_instances: 1
    max_instances: 3
    auto_scale: true
    
  # Health Check Configuration
  health_check:
    path: /health
    interval: 30s
    timeout: 10s
    retries: 3
    
# Database Configuration (if using external database)
# database:
#   type: postgresql
#   name: ider_kopi_db
#   version: "14"

# Storage Configuration (if needed)
# storage:
#   - name: uploads
#     size: 1GB
#     mount_path: /app/uploads

# Backup Configuration
backup:
  enabled: false
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention: 7  # Keep 7 days of backups

# Monitoring Configuration
monitoring:
  enabled: true
  alerts:
    - type: uptime
      threshold: 99%
    - type: response_time
      threshold: 2000ms
    - type: memory_usage
      threshold: 80%

# Security Configuration
security:
  headers:
    - "X-Frame-Options: SAMEORIGIN"
    - "X-XSS-Protection: 1; mode=block"
    - "X-Content-Type-Options: nosniff"
    - "Referrer-Policy: no-referrer-when-downgrade"
  
  # Rate Limiting
  rate_limit:
    enabled: true
    requests_per_minute: 100
    
# Caching Configuration
cache:
  enabled: true
  static_files:
    max_age: 31536000  # 1 year for static assets
  html_files:
    max_age: 3600      # 1 hour for HTML files
